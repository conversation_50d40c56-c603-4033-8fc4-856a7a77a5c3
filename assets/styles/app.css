/* Ensure proper contrast in both light and dark modes */
.card {
    border: 1px solid rgba(var(--bs-body-color-rgb), 0.125);
}

.card-header {
    background-color: rgba(var(--bs-body-color-rgb), 0.03);
    border-bottom: 1px solid rgba(var(--bs-body-color-rgb), 0.125);
}

/* Custom metric cards styling */
.metric-card {
    transition: transform 0.2s ease-in-out;
}

.metric-card:hover {
    transform: translateY(-2px);
}

/* System health indicators */
.health-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.health-indicator.healthy {
    background-color: var(--bs-success);
}

.health-indicator.warning {
    background-color: var(--bs-warning);
}

.health-indicator.danger {
    background-color: var(--bs-danger);
}
