# DiaSys - Healthcare Diagnostics Management System

A modern, comprehensive healthcare diagnostics management system built with Symfony 7.3.x, API Platform 4.x, and EasyAdmin 4.x. This system provides a complete solution for managing diagnostic services, patient records, appointments, test results, and financial operations in healthcare facilities.

## 🚀 Features

### Core Functionality
- **Patient Management**: Comprehensive patient profiles with medical history tracking
- **Doctor Management**: Provider profiles, scheduling, and performance analytics
- **Appointment System**: Intelligent booking with conflict detection and reminders
- **Laboratory Testing**: Test ordering, result management, and quality control
- **Financial Management**: Invoicing, payments, discounts, and financial reporting
- **User Authentication**: JWT-based authentication with role-based access control
- **Audit Logging**: Complete audit trail for compliance and security

### Technical Features
- **REST API**: Full REST API with OpenAPI 3.1 documentation
- **Admin Panel**: Modern admin interface with EasyAdmin 4.x
- **Real-time Updates**: Mercure integration for live notifications
- **File Uploads**: Secure file handling with S3-compatible storage support
- **Email Integration**: Multi-provider email system (SMTP, SendGrid, etc.)
- **Database**: PostgreSQL with optimized queries and indexing
- **Caching**: Redis integration for performance optimization
- **Security**: CSRF protection, XSS prevention, and secure authentication

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **PHP 8.3+** with required extensions:
  - `ext-ctype`
  - `ext-iconv`
  - `ext-pdo`
  - `ext-pdo_pgsql`
- **Composer** (PHP dependency manager)
- **Docker & Docker Compose** (for services)
- **Symfony CLI** (recommended for development)
- **Git** (for version control)

### System Requirements
- **Operating System**: Linux, macOS, or Windows (with WSL2)
- **Memory**: Minimum 2GB RAM, recommended 4GB+
- **Storage**: 500MB+ free space for application and data

## 🛠️ Installation

### 1. Clone the Repository

```bash
git clone <repository-url>
cd diasys
```

### 2. Install PHP Dependencies

```bash
composer install
```

### 3. Start Docker Services

The project uses Docker Compose for external services (database, Redis, Mercure, Mailpit):

```bash
docker compose up -d
```

This will start:
- **PostgreSQL** database on port 15432
- **Redis** cache service on port 6379
- **Mercure** real-time hub on port 80
- **Mailpit** email testing on ports 1025/8025

### 4. Environment Configuration

The project comes with pre-configured environment files. The main configuration is in `.env`:

```bash
# Database connection (already configured for Docker)
DATABASE_URL="postgresql://diasys:qrVWDn72ige1@127.0.0.1:15432/symfony?serverVersion=16&charset=utf8"

# JWT Configuration (keys are already generated)
JWT_SECRET_KEY=%kernel.project_dir%/config/jwt/private.pem
JWT_PUBLIC_KEY=%kernel.project_dir%/config/jwt/public.pem

# Mailer Configuration
MAILER_DSN=smtp://localhost:1025  # Uses Mailpit for development
```

### 5. Database Setup

Run database migrations to create the schema:

```bash
php bin/console doctrine:migrations:migrate --no-interaction
```

### 6. Load Test Data

Load sample data for development and testing:

```bash
php bin/console doctrine:fixtures:load --no-interaction
```

This will create sample users, patients, doctors, tests, and appointments.

### 7. Start Development Server

```bash
symfony serve
```

Or using PHP directly:

```bash
php bin/console cache:clear
php bin/console cache:warmup
php -S localhost:8000 -t public
```

The application will be available at: **https://127.0.0.1:8000**

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

```bash
# Application Environment
APP_ENV=dev
APP_SECRET=your-secret-key

# Database
DATABASE_URL="postgresql://user:password@host:port/dbname"

# JWT Authentication
JWT_SECRET_KEY=%kernel.project_dir%/config/jwt/private.pem
JWT_PUBLIC_KEY=%kernel.project_dir%/config/jwt/public.pem
JWT_PASSPHRASE=your-passphrase

# Email Configuration
MAILER_DSN=smtp://localhost:1025
# Or for production:
# MAILER_DSN=sendgrid://YOUR_API_KEY@default

# File Storage (S3)
S3_BUCKET=your-bucket-name
S3_REGION=us-east-1
S3_KEY=your-access-key
S3_SECRET=your-secret-key

# Mercure (Real-time updates)
MERCURE_URL=http://localhost/.well-known/mercure
MERCURE_PUBLIC_URL=http://localhost/.well-known/mercure
MERCURE_JWT_SECRET=your-mercure-secret
```

### JWT Key Generation

If you need to regenerate JWT keys:

```bash
php bin/console lexik:jwt:generate-keypair
```

## 🌐 Usage

### Web Interface

- **Main Application**: https://127.0.0.1:8000
- **Admin Panel**: https://127.0.0.1:8000/admin
- **API Documentation**: https://127.0.0.1:8000/api/docs

### API Endpoints

The system provides a comprehensive REST API:

```bash
# Authentication
POST /api/auth/login
POST /api/auth/register

# Patients
GET    /api/patients
POST   /api/patients
GET    /api/patients/{id}
PUT    /api/patients/{id}
DELETE /api/patients/{id}

# Appointments
GET    /api/appointments
POST   /api/appointments
GET    /api/appointments/{id}

# Tests
GET    /api/tests
POST   /api/tests
GET    /api/tests/{id}/results

# And many more endpoints...
```

### Default Test Users

After loading fixtures, you can use these test accounts:

- **Admin User**: <EMAIL> / password123
- **Doctor User**: <EMAIL> / password123
- **Patient User**: <EMAIL> / password123

## 🧪 Testing

### Manual Testing

Test the API using curl or tools like Postman:

```bash
# Login to get JWT token
curl -X POST https://127.0.0.1:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Use the token for authenticated requests
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  https://127.0.0.1:8000/api/patients
```

### Automated Testing

Run the test suite:

```bash
php bin/phpunit
```

## 📊 Monitoring & Logs

### Application Logs

```bash
# View Symfony logs
php bin/console log:display

# View specific log file
tail -f var/log/dev.log
```

### Docker Services

```bash
# View service logs
docker compose logs database
docker compose logs redis
docker compose logs mercure

# Access database directly
docker compose exec database psql -U diasys -d symfony
```

### Email Testing

Mailpit provides a web interface for testing emails:
- **Web Interface**: http://localhost:8025
- **SMTP Server**: localhost:1025

## 🚀 Deployment

### Production Deployment

1. **Environment Setup**:
   ```bash
   APP_ENV=prod
   APP_SECRET=your-production-secret
   ```

2. **Database Migration**:
   ```bash
   php bin/console doctrine:migrations:migrate --env=prod
   ```

3. **Cache Clear**:
   ```bash
   php bin/console cache:clear --env=prod
   php bin/console cache:warmup --env=prod
   ```

4. **Assets Build** (if using asset mapper):
   ```bash
   php bin/console asset-map:compile
   ```

### Docker Production

For production deployment with Docker:

```bash
docker compose -f compose.prod.yaml up -d
```

## 🤝 Contributing

### Development Workflow

1. **Fork** the repository
2. **Create** a feature branch: `git checkout -b feature/your-feature`
3. **Make** your changes following the commit guidelines
4. **Test** thoroughly
5. **Submit** a pull request

### Commit Guidelines

This project follows conventional commits with gitmoji:

```bash
✨ feat: add new patient search functionality
🐛 fix: resolve appointment booking conflict
📝 docs: update API documentation
♻️ refactor: optimize database queries
```

### Code Standards

- Follow **Symfony coding standards**
- Use **PHP 8.3+ features** (attributes, union types, etc.)
- Write **comprehensive tests** for new features
- Maintain **API Platform best practices**

## 📚 Documentation

### API Documentation

- **Swagger UI**: `/api/docs`
- **OpenAPI Spec**: `/api/docs.json`
- **GraphQL** (if enabled): `/api/graphql`

### Architecture Documentation

- **Entity Relationships**: See entity classes in `src/Entity/`
- **API Resources**: API Platform configurations
- **Security**: Authentication and authorization setup

## 🔒 Security

### Security Features

- **JWT Authentication** with secure token handling
- **Role-based Access Control** (RBAC)
- **CSRF Protection** on all forms
- **XSS Prevention** with Twig escaping
- **SQL Injection Prevention** with Doctrine
- **Audit Logging** for all data changes
- **Secure File Uploads** with validation

### Security Best Practices

- Regular dependency updates
- Security headers configuration
- Input validation and sanitization
- Secure password hashing
- Session security management

## 📄 License

This project is proprietary software. All rights reserved.

## 🆘 Support

### Common Issues

1. **Database Connection Issues**:
   ```bash
   docker compose restart database
   php bin/console doctrine:database:create
   ```

2. **Permission Issues**:
   ```bash
   chown -R www-data:www-data var/
   chmod -R 755 var/
   ```

3. **Cache Issues**:
   ```bash
   php bin/console cache:clear
   php bin/console cache:warmup
   ```

### Getting Help

- Check the **API documentation** at `/api/docs`
- Review **Symfony logs** in `var/log/`
- Test with **Postman** or curl commands
- Check **Docker service status**: `docker compose ps`

---

**Built with ❤️ using Symfony 7.3.x, API Platform 4.x, and EasyAdmin 4.x**