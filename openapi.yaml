openapi: 3.1.0
info:
 title: 'DiaSys Healthcare API'
  description: 'REST API for DiaSys Healthcare Management System'
  version: 3.0.0-beta
servers:
  -
    url: /
    description: ''
paths:
  /api/audit_logs:
    get:
      operationId: api_audit_logs_get_collection
      tags:
        - AuditLog
      responses:
        '200':
          description: 'AuditLog collection'
          content:
            application/ld+json:
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/AuditLog.jsonld-audit_log.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            'application/ld+json; version=1.0':
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/AuditLog.jsonld-audit_log.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AuditLog-audit_log.read'
            'application/json; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AuditLog-audit_log.read'
            application/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AuditLog-audit_log.read'
            text/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AuditLog-audit_log.read'
            'application/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AuditLog-audit_log.read'
            'text/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AuditLog-audit_log.read'
            text/html:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AuditLog-audit_log.read'
            'text/html; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AuditLog-audit_log.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves the collection of AuditLog resources.'
      description: 'Retrieves the collection of AuditLog resources.'
      parameters:
        -
          name: page
          in: query
          description: 'The collection page number'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 1
          style: form
          explode: false
          allowReserved: false
        -
          name: itemsPerPage
          in: query
          description: 'The number of items per page'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 30
            minimum: 0
            maximum: 100
          style: form
          explode: false
          allowReserved: false
        -
          name: pagination
          in: query
          description: 'Enable or disable pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
        -
          name: partial
          in: query
          description: 'Enable or disable partial pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
      deprecated: false
    post:
      operationId: api_audit_logs_post
      tags:
        - AuditLog
      responses:
        '201':
          description: 'AuditLog resource created'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/AuditLog.jsonld-audit_log.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/AuditLog.jsonld-audit_log.read'
            application/json:
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
            text/html:
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Creates a AuditLog resource.'
      description: 'Creates a AuditLog resource.'
      parameters: []
      requestBody:
        description: 'The new AuditLog resource'
        content:
          application/ld+json:
            schema:
              $ref: '#/components/schemas/AuditLog.jsonld'
          'application/ld+json; version=1.0':
            schema:
              $ref: '#/components/schemas/AuditLog.jsonld'
          application/json:
            schema:
              $ref: '#/components/schemas/AuditLog'
          'application/json; version=1.0':
            schema:
              $ref: '#/components/schemas/AuditLog'
          application/xml:
            schema:
              $ref: '#/components/schemas/AuditLog'
          text/xml:
            schema:
              $ref: '#/components/schemas/AuditLog'
          'application/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/AuditLog'
          'text/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/AuditLog'
          text/html:
            schema:
              $ref: '#/components/schemas/AuditLog'
          'text/html; version=1.0':
            schema:
              $ref: '#/components/schemas/AuditLog'
        required: true
      deprecated: false
  '/api/audit_logs/{id}':
    get:
      operationId: api_audit_logs_id_get
      tags:
        - AuditLog
      responses:
        '200':
          description: 'AuditLog resource'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/AuditLog.jsonld-audit_log.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/AuditLog.jsonld-audit_log.read'
            application/json:
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
            text/html:
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves a AuditLog resource.'
      description: 'Retrieves a AuditLog resource.'
      parameters:
        -
          name: id
          in: path
          description: 'AuditLog identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    delete:
      operationId: api_audit_logs_id_delete
      tags:
        - AuditLog
      responses:
        '204':
          description: 'AuditLog resource deleted'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Removes the AuditLog resource.'
      description: 'Removes the AuditLog resource.'
      parameters:
        -
          name: id
          in: path
          description: 'AuditLog identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    patch:
      operationId: api_audit_logs_id_patch
      tags:
        - AuditLog
      responses:
        '200':
          description: 'AuditLog resource updated'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/AuditLog.jsonld-audit_log.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/AuditLog.jsonld-audit_log.read'
            application/json:
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
            text/html:
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/AuditLog-audit_log.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Updates the AuditLog resource.'
      description: 'Updates the AuditLog resource.'
      parameters:
        -
          name: id
          in: path
          description: 'AuditLog identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      requestBody:
        description: 'The updated AuditLog resource'
        content:
          application/merge-patch+json:
            schema:
              $ref: '#/components/schemas/AuditLog'
        required: true
      deprecated: false
  /api/configuration_histories:
    get:
      operationId: api_configuration_histories_get_collection
      tags:
        - ConfigurationHistory
      responses:
        '200':
          description: 'ConfigurationHistory collection'
          content:
            application/ld+json:
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/ConfigurationHistory.jsonld-config_history.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            'application/ld+json; version=1.0':
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/ConfigurationHistory.jsonld-config_history.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            'application/json; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            application/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            text/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            'application/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            'text/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            text/html:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            'text/html; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves the collection of ConfigurationHistory resources.'
      description: 'Retrieves the collection of ConfigurationHistory resources.'
      parameters:
        -
          name: page
          in: query
          description: 'The collection page number'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 1
          style: form
          explode: false
          allowReserved: false
        -
          name: itemsPerPage
          in: query
          description: 'The number of items per page'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 30
            minimum: 0
            maximum: 100
          style: form
          explode: false
          allowReserved: false
        -
          name: pagination
          in: query
          description: 'Enable or disable pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
        -
          name: partial
          in: query
          description: 'Enable or disable partial pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
      deprecated: false
    post:
      operationId: api_configuration_histories_post
      tags:
        - ConfigurationHistory
      responses:
        '201':
          description: 'ConfigurationHistory resource created'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConfigurationHistory.jsonld-config_history.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/ConfigurationHistory.jsonld-config_history.read'
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            text/html:
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Creates a ConfigurationHistory resource.'
      description: 'Creates a ConfigurationHistory resource.'
      parameters: []
      requestBody:
        description: 'The new ConfigurationHistory resource'
        content:
          application/ld+json:
            schema:
              $ref: '#/components/schemas/ConfigurationHistory.jsonld'
          'application/ld+json; version=1.0':
            schema:
              $ref: '#/components/schemas/ConfigurationHistory.jsonld'
          application/json:
            schema:
              $ref: '#/components/schemas/ConfigurationHistory'
          'application/json; version=1.0':
            schema:
              $ref: '#/components/schemas/ConfigurationHistory'
          application/xml:
            schema:
              $ref: '#/components/schemas/ConfigurationHistory'
          text/xml:
            schema:
              $ref: '#/components/schemas/ConfigurationHistory'
          'application/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/ConfigurationHistory'
          'text/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/ConfigurationHistory'
          text/html:
            schema:
              $ref: '#/components/schemas/ConfigurationHistory'
          'text/html; version=1.0':
            schema:
              $ref: '#/components/schemas/ConfigurationHistory'
        required: true
      deprecated: false
  '/api/configuration_histories/{id}':
    get:
      operationId: api_configuration_histories_id_get
      tags:
        - ConfigurationHistory
      responses:
        '200':
          description: 'ConfigurationHistory resource'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConfigurationHistory.jsonld-config_history.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/ConfigurationHistory.jsonld-config_history.read'
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            text/html:
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves a ConfigurationHistory resource.'
      description: 'Retrieves a ConfigurationHistory resource.'
      parameters:
        -
          name: id
          in: path
          description: 'ConfigurationHistory identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    delete:
      operationId: api_configuration_histories_id_delete
      tags:
        - ConfigurationHistory
      responses:
        '204':
          description: 'ConfigurationHistory resource deleted'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Removes the ConfigurationHistory resource.'
      description: 'Removes the ConfigurationHistory resource.'
      parameters:
        -
          name: id
          in: path
          description: 'ConfigurationHistory identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    patch:
      operationId: api_configuration_histories_id_patch
      tags:
        - ConfigurationHistory
      responses:
        '200':
          description: 'ConfigurationHistory resource updated'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConfigurationHistory.jsonld-config_history.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/ConfigurationHistory.jsonld-config_history.read'
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            text/html:
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/ConfigurationHistory-config_history.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Updates the ConfigurationHistory resource.'
      description: 'Updates the ConfigurationHistory resource.'
      parameters:
        -
          name: id
          in: path
          description: 'ConfigurationHistory identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      requestBody:
        description: 'The updated ConfigurationHistory resource'
        content:
          application/merge-patch+json:
            schema:
              $ref: '#/components/schemas/ConfigurationHistory'
        required: true
      deprecated: false
  /api/doctors:
    get:
      operationId: api_doctors_get_collection
      tags:
        - Doctor
      responses:
        '200':
          description: 'Doctor collection'
          content:
            application/ld+json:
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/Doctor.jsonld-doctor.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            'application/ld+json; version=1.0':
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/Doctor.jsonld-doctor.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Doctor-doctor.read'
            'application/json; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Doctor-doctor.read'
            application/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Doctor-doctor.read'
            text/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Doctor-doctor.read'
            'application/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Doctor-doctor.read'
            'text/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Doctor-doctor.read'
            text/html:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Doctor-doctor.read'
            'text/html; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Doctor-doctor.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves the collection of Doctor resources.'
      description: 'Retrieves the collection of Doctor resources.'
      parameters:
        -
          name: page
          in: query
          description: 'The collection page number'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 1
          style: form
          explode: false
          allowReserved: false
        -
          name: itemsPerPage
          in: query
          description: 'The number of items per page'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 30
            minimum: 0
            maximum: 100
          style: form
          explode: false
          allowReserved: false
        -
          name: pagination
          in: query
          description: 'Enable or disable pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
        -
          name: partial
          in: query
          description: 'Enable or disable partial pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
      deprecated: false
    post:
      operationId: api_doctors_post
      tags:
        - Doctor
      responses:
        '201':
          description: 'Doctor resource created'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Doctor.jsonld-doctor.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/Doctor.jsonld-doctor.read'
            application/json:
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
            text/html:
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Creates a Doctor resource.'
      description: 'Creates a Doctor resource.'
      parameters: []
      requestBody:
        description: 'The new Doctor resource'
        content:
          application/ld+json:
            schema:
              $ref: '#/components/schemas/Doctor.jsonld-doctor.write'
          'application/ld+json; version=1.0':
            schema:
              $ref: '#/components/schemas/Doctor.jsonld-doctor.write'
          application/json:
            schema:
              $ref: '#/components/schemas/Doctor-doctor.write'
          'application/json; version=1.0':
            schema:
              $ref: '#/components/schemas/Doctor-doctor.write'
          application/xml:
            schema:
              $ref: '#/components/schemas/Doctor-doctor.write'
          text/xml:
            schema:
              $ref: '#/components/schemas/Doctor-doctor.write'
          'application/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/Doctor-doctor.write'
          'text/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/Doctor-doctor.write'
          text/html:
            schema:
              $ref: '#/components/schemas/Doctor-doctor.write'
          'text/html; version=1.0':
            schema:
              $ref: '#/components/schemas/Doctor-doctor.write'
        required: true
      deprecated: false
  '/api/doctors/{id}':
    get:
      operationId: api_doctors_id_get
      tags:
        - Doctor
      responses:
        '200':
          description: 'Doctor resource'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Doctor.jsonld-doctor.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/Doctor.jsonld-doctor.read'
            application/json:
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
            text/html:
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves a Doctor resource.'
      description: 'Retrieves a Doctor resource.'
      parameters:
        -
          name: id
          in: path
          description: 'Doctor identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    delete:
      operationId: api_doctors_id_delete
      tags:
        - Doctor
      responses:
        '204':
          description: 'Doctor resource deleted'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Removes the Doctor resource.'
      description: 'Removes the Doctor resource.'
      parameters:
        -
          name: id
          in: path
          description: 'Doctor identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    patch:
      operationId: api_doctors_id_patch
      tags:
        - Doctor
      responses:
        '200':
          description: 'Doctor resource updated'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Doctor.jsonld-doctor.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/Doctor.jsonld-doctor.read'
            application/json:
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
            text/html:
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/Doctor-doctor.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Updates the Doctor resource.'
      description: 'Updates the Doctor resource.'
      parameters:
        -
          name: id
          in: path
          description: 'Doctor identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      requestBody:
        description: 'The updated Doctor resource'
        content:
          application/merge-patch+json:
            schema:
              $ref: '#/components/schemas/Doctor-doctor.write'
        required: true
      deprecated: false
  /api/api/auth/forgot-password:
    post:
      operationId: api_apiauthforgot-password_post
      tags:
        - ForgotPassword
      responses:
        '200':
          description: 'Forgot password request processed'
          content:
            application/json:
              schema:
                type: object
                properties:
                  message: { type: string, example: 'If an account with that email exists, we have sent you a password reset link.' }
        '400':
          description: 'Bad request'
          content:
            application/json:
              schema:
                type: object
                properties:
                  code: { type: integer }
                  message: { type: string }
        '201':
          description: 'ForgotPassword resource created'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ForgotPassword.jsonld'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/ForgotPassword.jsonld'
            application/json:
              schema:
                $ref: '#/components/schemas/ForgotPassword'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/ForgotPassword'
            application/xml:
              schema:
                $ref: '#/components/schemas/ForgotPassword'
            text/xml:
              schema:
                $ref: '#/components/schemas/ForgotPassword'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/ForgotPassword'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/ForgotPassword'
            text/html:
              schema:
                $ref: '#/components/schemas/ForgotPassword'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/ForgotPassword'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
      summary: 'Request password reset'
      description: 'Request a password reset link'
      parameters: []
      requestBody:
        description: 'The new ForgotPassword resource'
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
              required:
                - email
        required: false
      deprecated: false
  /api/auth/login:
    post:
      operationId: login_check_post
      tags:
        - 'Login Check'
      responses:
        '200':
          description: 'User token created'
          content:
            application/json:
              schema:
                type: object
                properties:
                  token: { readOnly: true, type: string, nullable: false }
                required:
                  - token
      summary: 'Creates a user token.'
      description: 'Creates a user token.'
      requestBody:
        description: 'The login data'
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  nullable: false
                password:
                  type: string
                  nullable: false
              required:
                - email
                - password
        required: true
  /api/api/auth/logout:
    post:
      operationId: api_apiauthlogout_post
      tags:
        - Logout
      responses:
        '200':
          description: 'Logged out successfully'
          content:
            application/json:
              schema:
                type: object
                properties:
                  message: { type: string, example: 'Successfully logged out' }
        '201':
          description: 'Logout resource created'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Logout.jsonld'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/Logout.jsonld'
            application/json:
              schema:
                $ref: '#/components/schemas/Logout'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/Logout'
            application/xml:
              schema:
                $ref: '#/components/schemas/Logout'
            text/xml:
              schema:
                $ref: '#/components/schemas/Logout'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Logout'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Logout'
            text/html:
              schema:
                $ref: '#/components/schemas/Logout'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/Logout'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
      summary: Logout
      description: 'Logout from the application'
      parameters: []
      requestBody:
        description: 'The new Logout resource'
        content:
          application/json:
            schema:
              type: object
              properties: []
        required: false
      deprecated: false
  /api/patients:
    get:
      operationId: api_patients_get_collection
      tags:
        - Patient
      responses:
        '200':
          description: 'Patient collection'
          content:
            application/ld+json:
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/Patient.jsonld-patient.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            'application/ld+json; version=1.0':
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/Patient.jsonld-patient.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Patient-patient.read'
            'application/json; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Patient-patient.read'
            application/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Patient-patient.read'
            text/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Patient-patient.read'
            'application/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Patient-patient.read'
            'text/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Patient-patient.read'
            text/html:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Patient-patient.read'
            'text/html; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Patient-patient.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves the collection of Patient resources.'
      description: 'Retrieves the collection of Patient resources.'
      parameters:
        -
          name: page
          in: query
          description: 'The collection page number'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 1
          style: form
          explode: false
          allowReserved: false
        -
          name: itemsPerPage
          in: query
          description: 'The number of items per page'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 30
            minimum: 0
            maximum: 100
          style: form
          explode: false
          allowReserved: false
        -
          name: pagination
          in: query
          description: 'Enable or disable pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
        -
          name: partial
          in: query
          description: 'Enable or disable partial pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
      deprecated: false
    post:
      operationId: api_patients_post
      tags:
        - Patient
      responses:
        '201':
          description: 'Patient resource created'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Patient.jsonld-patient.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/Patient.jsonld-patient.read'
            application/json:
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
            text/html:
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Creates a Patient resource.'
      description: 'Creates a Patient resource.'
      parameters: []
      requestBody:
        description: 'The new Patient resource'
        content:
          application/ld+json:
            schema:
              $ref: '#/components/schemas/Patient.jsonld-patient.write'
          'application/ld+json; version=1.0':
            schema:
              $ref: '#/components/schemas/Patient.jsonld-patient.write'
          application/json:
            schema:
              $ref: '#/components/schemas/Patient-patient.write'
          'application/json; version=1.0':
            schema:
              $ref: '#/components/schemas/Patient-patient.write'
          application/xml:
            schema:
              $ref: '#/components/schemas/Patient-patient.write'
          text/xml:
            schema:
              $ref: '#/components/schemas/Patient-patient.write'
          'application/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/Patient-patient.write'
          'text/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/Patient-patient.write'
          text/html:
            schema:
              $ref: '#/components/schemas/Patient-patient.write'
          'text/html; version=1.0':
            schema:
              $ref: '#/components/schemas/Patient-patient.write'
        required: true
      deprecated: false
  '/api/patients/{id}':
    get:
      operationId: api_patients_id_get
      tags:
        - Patient
      responses:
        '200':
          description: 'Patient resource'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Patient.jsonld-patient.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/Patient.jsonld-patient.read'
            application/json:
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
            text/html:
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves a Patient resource.'
      description: 'Retrieves a Patient resource.'
      parameters:
        -
          name: id
          in: path
          description: 'Patient identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    delete:
      operationId: api_patients_id_delete
      tags:
        - Patient
      responses:
        '204':
          description: 'Patient resource deleted'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Removes the Patient resource.'
      description: 'Removes the Patient resource.'
      parameters:
        -
          name: id
          in: path
          description: 'Patient identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    patch:
      operationId: api_patients_id_patch
      tags:
        - Patient
      responses:
        '200':
          description: 'Patient resource updated'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Patient.jsonld-patient.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/Patient.jsonld-patient.read'
            application/json:
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
            text/html:
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/Patient-patient.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Updates the Patient resource.'
      description: 'Updates the Patient resource.'
      parameters:
        -
          name: id
          in: path
          description: 'Patient identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      requestBody:
        description: 'The updated Patient resource'
        content:
          application/merge-patch+json:
            schema:
              $ref: '#/components/schemas/Patient-patient.write'
        required: true
      deprecated: false
  /api/permissions:
    get:
      operationId: api_permissions_get_collection
      tags:
        - Permission
      responses:
        '200':
          description: 'Permission collection'
          content:
            application/ld+json:
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/Permission.jsonld-permission.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            'application/ld+json; version=1.0':
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/Permission.jsonld-permission.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Permission-permission.read'
            'application/json; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Permission-permission.read'
            application/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Permission-permission.read'
            text/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Permission-permission.read'
            'application/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Permission-permission.read'
            'text/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Permission-permission.read'
            text/html:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Permission-permission.read'
            'text/html; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Permission-permission.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves the collection of Permission resources.'
      description: 'Retrieves the collection of Permission resources.'
      parameters:
        -
          name: page
          in: query
          description: 'The collection page number'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 1
          style: form
          explode: false
          allowReserved: false
        -
          name: itemsPerPage
          in: query
          description: 'The number of items per page'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 30
            minimum: 0
            maximum: 100
          style: form
          explode: false
          allowReserved: false
        -
          name: pagination
          in: query
          description: 'Enable or disable pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
        -
          name: partial
          in: query
          description: 'Enable or disable partial pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
      deprecated: false
    post:
      operationId: api_permissions_post
      tags:
        - Permission
      responses:
        '201':
          description: 'Permission resource created'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Permission.jsonld-permission.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/Permission.jsonld-permission.read'
            application/json:
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
            text/html:
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Creates a Permission resource.'
      description: 'Creates a Permission resource.'
      parameters: []
      requestBody:
        description: 'The new Permission resource'
        content:
          application/ld+json:
            schema:
              $ref: '#/components/schemas/Permission.jsonld-permission.write'
          'application/ld+json; version=1.0':
            schema:
              $ref: '#/components/schemas/Permission.jsonld-permission.write'
          application/json:
            schema:
              $ref: '#/components/schemas/Permission-permission.write'
          'application/json; version=1.0':
            schema:
              $ref: '#/components/schemas/Permission-permission.write'
          application/xml:
            schema:
              $ref: '#/components/schemas/Permission-permission.write'
          text/xml:
            schema:
              $ref: '#/components/schemas/Permission-permission.write'
          'application/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/Permission-permission.write'
          'text/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/Permission-permission.write'
          text/html:
            schema:
              $ref: '#/components/schemas/Permission-permission.write'
          'text/html; version=1.0':
            schema:
              $ref: '#/components/schemas/Permission-permission.write'
        required: true
      deprecated: false
  '/api/permissions/{id}':
    get:
      operationId: api_permissions_id_get
      tags:
        - Permission
      responses:
        '200':
          description: 'Permission resource'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Permission.jsonld-permission.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/Permission.jsonld-permission.read'
            application/json:
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
            text/html:
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves a Permission resource.'
      description: 'Retrieves a Permission resource.'
      parameters:
        -
          name: id
          in: path
          description: 'Permission identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    delete:
      operationId: api_permissions_id_delete
      tags:
        - Permission
      responses:
        '204':
          description: 'Permission resource deleted'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Removes the Permission resource.'
      description: 'Removes the Permission resource.'
      parameters:
        -
          name: id
          in: path
          description: 'Permission identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    patch:
      operationId: api_permissions_id_patch
      tags:
        - Permission
      responses:
        '200':
          description: 'Permission resource updated'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Permission.jsonld-permission.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/Permission.jsonld-permission.read'
            application/json:
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
            text/html:
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/Permission-permission.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Updates the Permission resource.'
      description: 'Updates the Permission resource.'
      parameters:
        -
          name: id
          in: path
          description: 'Permission identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      requestBody:
        description: 'The updated Permission resource'
        content:
          application/merge-patch+json:
            schema:
              $ref: '#/components/schemas/Permission-permission.write'
        required: true
      deprecated: false
  /api/api/auth/register:
    post:
      operationId: api_apiauthregister_post
      tags:
        - Register
      responses:
        '201':
          description: 'Registration successful'
          content:
            application/json:
              schema:
                type: object
                properties:
                  message: { type: string, example: 'Registration successful! Please check your email for verification.' }
        '400':
          description: 'Bad request'
          content:
            application/json:
              schema:
                type: object
                properties:
                  code: { type: integer }
                  message: { type: string }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
      summary: 'Register a new user'
      description: 'Register a new user account'
      parameters: []
      requestBody:
        description: 'The new Register resource'
        content:
          application/json:
            schema:
              type: object
              properties:
                firstName:
                  type: string
                  example: John
                lastName:
                  type: string
                  example: Doe
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                password:
                  type: string
                  format: password
                  example: securePassword123
                confirmPassword:
                  type: string
                  format: password
                  example: securePassword123
              required:
                - firstName
                - lastName
                - email
                - password
                - confirmPassword
        required: false
      deprecated: false
  /api/api/auth/reset-password:
    post:
      operationId: api_apiauthreset-password_post
      tags:
        - ResetPassword
      responses:
        '200':
          description: 'Password reset successful'
          content:
            application/json:
              schema:
                type: object
                properties:
                  message: { type: string, example: 'Password reset successfully!' }
        '400':
          description: 'Bad request'
          content:
            application/json:
              schema:
                type: object
                properties:
                  code: { type: integer }
                  message: { type: string }
        '201':
          description: 'ResetPassword resource created'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ResetPassword.jsonld'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/ResetPassword.jsonld'
            application/json:
              schema:
                $ref: '#/components/schemas/ResetPassword'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/ResetPassword'
            application/xml:
              schema:
                $ref: '#/components/schemas/ResetPassword'
            text/xml:
              schema:
                $ref: '#/components/schemas/ResetPassword'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/ResetPassword'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/ResetPassword'
            text/html:
              schema:
                $ref: '#/components/schemas/ResetPassword'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/ResetPassword'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
      summary: 'Reset password'
      description: 'Reset user password'
      parameters: []
      requestBody:
        description: 'The new ResetPassword resource'
        content:
          application/json:
            schema:
              type: object
              properties:
                token:
                  type: string
                  example: reset_token_here
                password:
                  type: string
                  format: password
                  example: newSecurePassword123
                confirmPassword:
                  type: string
                  format: password
                  example: newSecurePassword123
              required:
                - token
                - password
                - confirmPassword
        required: false
      deprecated: false
  /api/roles:
    get:
      operationId: api_roles_get_collection
      tags:
        - Role
      responses:
        '200':
          description: 'Role collection'
          content:
            application/ld+json:
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/Role.jsonld-role.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            'application/ld+json; version=1.0':
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/Role.jsonld-role.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Role-role.read'
            'application/json; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Role-role.read'
            application/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Role-role.read'
            text/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Role-role.read'
            'application/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Role-role.read'
            'text/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Role-role.read'
            text/html:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Role-role.read'
            'text/html; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Role-role.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves the collection of Role resources.'
      description: 'Retrieves the collection of Role resources.'
      parameters:
        -
          name: page
          in: query
          description: 'The collection page number'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 1
          style: form
          explode: false
          allowReserved: false
        -
          name: itemsPerPage
          in: query
          description: 'The number of items per page'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 30
            minimum: 0
            maximum: 100
          style: form
          explode: false
          allowReserved: false
        -
          name: pagination
          in: query
          description: 'Enable or disable pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
        -
          name: partial
          in: query
          description: 'Enable or disable partial pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
      deprecated: false
    post:
      operationId: api_roles_post
      tags:
        - Role
      responses:
        '201':
          description: 'Role resource created'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Role.jsonld-role.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/Role.jsonld-role.read'
            application/json:
              schema:
                $ref: '#/components/schemas/Role-role.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/Role-role.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/Role-role.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/Role-role.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Role-role.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Role-role.read'
            text/html:
              schema:
                $ref: '#/components/schemas/Role-role.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/Role-role.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Creates a Role resource.'
      description: 'Creates a Role resource.'
      parameters: []
      requestBody:
        description: 'The new Role resource'
        content:
          application/ld+json:
            schema:
              $ref: '#/components/schemas/Role.jsonld-role.write'
          'application/ld+json; version=1.0':
            schema:
              $ref: '#/components/schemas/Role.jsonld-role.write'
          application/json:
            schema:
              $ref: '#/components/schemas/Role-role.write'
          'application/json; version=1.0':
            schema:
              $ref: '#/components/schemas/Role-role.write'
          application/xml:
            schema:
              $ref: '#/components/schemas/Role-role.write'
          text/xml:
            schema:
              $ref: '#/components/schemas/Role-role.write'
          'application/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/Role-role.write'
          'text/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/Role-role.write'
          text/html:
            schema:
              $ref: '#/components/schemas/Role-role.write'
          'text/html; version=1.0':
            schema:
              $ref: '#/components/schemas/Role-role.write'
        required: true
      deprecated: false
  '/api/roles/{id}':
    get:
      operationId: api_roles_id_get
      tags:
        - Role
      responses:
        '200':
          description: 'Role resource'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Role.jsonld-role.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/Role.jsonld-role.read'
            application/json:
              schema:
                $ref: '#/components/schemas/Role-role.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/Role-role.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/Role-role.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/Role-role.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Role-role.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Role-role.read'
            text/html:
              schema:
                $ref: '#/components/schemas/Role-role.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/Role-role.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves a Role resource.'
      description: 'Retrieves a Role resource.'
      parameters:
        -
          name: id
          in: path
          description: 'Role identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    delete:
      operationId: api_roles_id_delete
      tags:
        - Role
      responses:
        '204':
          description: 'Role resource deleted'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Removes the Role resource.'
      description: 'Removes the Role resource.'
      parameters:
        -
          name: id
          in: path
          description: 'Role identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    patch:
      operationId: api_roles_id_patch
      tags:
        - Role
      responses:
        '200':
          description: 'Role resource updated'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Role.jsonld-role.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/Role.jsonld-role.read'
            application/json:
              schema:
                $ref: '#/components/schemas/Role-role.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/Role-role.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/Role-role.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/Role-role.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Role-role.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/Role-role.read'
            text/html:
              schema:
                $ref: '#/components/schemas/Role-role.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/Role-role.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Updates the Role resource.'
      description: 'Updates the Role resource.'
      parameters:
        -
          name: id
          in: path
          description: 'Role identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      requestBody:
        description: 'The updated Role resource'
        content:
          application/merge-patch+json:
            schema:
              $ref: '#/components/schemas/Role-role.write'
        required: true
      deprecated: false
  /api/role_permissions:
    get:
      operationId: api_role_permissions_get_collection
      tags:
        - RolePermission
      responses:
        '200':
          description: 'RolePermission collection'
          content:
            application/ld+json:
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/RolePermission.jsonld-role_permission.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            'application/ld+json; version=1.0':
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/RolePermission.jsonld-role_permission.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RolePermission-role_permission.read'
            'application/json; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RolePermission-role_permission.read'
            application/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RolePermission-role_permission.read'
            text/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RolePermission-role_permission.read'
            'application/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RolePermission-role_permission.read'
            'text/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RolePermission-role_permission.read'
            text/html:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RolePermission-role_permission.read'
            'text/html; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RolePermission-role_permission.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves the collection of RolePermission resources.'
      description: 'Retrieves the collection of RolePermission resources.'
      parameters:
        -
          name: page
          in: query
          description: 'The collection page number'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 1
          style: form
          explode: false
          allowReserved: false
        -
          name: itemsPerPage
          in: query
          description: 'The number of items per page'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 30
            minimum: 0
            maximum: 100
          style: form
          explode: false
          allowReserved: false
        -
          name: pagination
          in: query
          description: 'Enable or disable pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
        -
          name: partial
          in: query
          description: 'Enable or disable partial pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
      deprecated: false
    post:
      operationId: api_role_permissions_post
      tags:
        - RolePermission
      responses:
        '201':
          description: 'RolePermission resource created'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/RolePermission.jsonld-role_permission.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/RolePermission.jsonld-role_permission.read'
            application/json:
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
            text/html:
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Creates a RolePermission resource.'
      description: 'Creates a RolePermission resource.'
      parameters: []
      requestBody:
        description: 'The new RolePermission resource'
        content:
          application/ld+json:
            schema:
              $ref: '#/components/schemas/RolePermission.jsonld-role_permission.write'
          'application/ld+json; version=1.0':
            schema:
              $ref: '#/components/schemas/RolePermission.jsonld-role_permission.write'
          application/json:
            schema:
              $ref: '#/components/schemas/RolePermission-role_permission.write'
          'application/json; version=1.0':
            schema:
              $ref: '#/components/schemas/RolePermission-role_permission.write'
          application/xml:
            schema:
              $ref: '#/components/schemas/RolePermission-role_permission.write'
          text/xml:
            schema:
              $ref: '#/components/schemas/RolePermission-role_permission.write'
          'application/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/RolePermission-role_permission.write'
          'text/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/RolePermission-role_permission.write'
          text/html:
            schema:
              $ref: '#/components/schemas/RolePermission-role_permission.write'
          'text/html; version=1.0':
            schema:
              $ref: '#/components/schemas/RolePermission-role_permission.write'
        required: true
      deprecated: false
  '/api/role_permissions/{id}':
    get:
      operationId: api_role_permissions_id_get
      tags:
        - RolePermission
      responses:
        '200':
          description: 'RolePermission resource'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/RolePermission.jsonld-role_permission.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/RolePermission.jsonld-role_permission.read'
            application/json:
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
            text/html:
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves a RolePermission resource.'
      description: 'Retrieves a RolePermission resource.'
      parameters:
        -
          name: id
          in: path
          description: 'RolePermission identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    delete:
      operationId: api_role_permissions_id_delete
      tags:
        - RolePermission
      responses:
        '204':
          description: 'RolePermission resource deleted'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Removes the RolePermission resource.'
      description: 'Removes the RolePermission resource.'
      parameters:
        -
          name: id
          in: path
          description: 'RolePermission identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    patch:
      operationId: api_role_permissions_id_patch
      tags:
        - RolePermission
      responses:
        '200':
          description: 'RolePermission resource updated'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/RolePermission.jsonld-role_permission.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/RolePermission.jsonld-role_permission.read'
            application/json:
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
            text/html:
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/RolePermission-role_permission.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Updates the RolePermission resource.'
      description: 'Updates the RolePermission resource.'
      parameters:
        -
          name: id
          in: path
          description: 'RolePermission identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      requestBody:
        description: 'The updated RolePermission resource'
        content:
          application/merge-patch+json:
            schema:
              $ref: '#/components/schemas/RolePermission-role_permission.write'
        required: true
      deprecated: false
  /api/security_events:
    get:
      operationId: api_security_events_get_collection
      tags:
        - SecurityEvent
      responses:
        '200':
          description: 'SecurityEvent collection'
          content:
            application/ld+json:
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/SecurityEvent.jsonld-security_event.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            'application/ld+json; version=1.0':
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/SecurityEvent.jsonld-security_event.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SecurityEvent-security_event.read'
            'application/json; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SecurityEvent-security_event.read'
            application/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SecurityEvent-security_event.read'
            text/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SecurityEvent-security_event.read'
            'application/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SecurityEvent-security_event.read'
            'text/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SecurityEvent-security_event.read'
            text/html:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SecurityEvent-security_event.read'
            'text/html; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SecurityEvent-security_event.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves the collection of SecurityEvent resources.'
      description: 'Retrieves the collection of SecurityEvent resources.'
      parameters:
        -
          name: page
          in: query
          description: 'The collection page number'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 1
          style: form
          explode: false
          allowReserved: false
        -
          name: itemsPerPage
          in: query
          description: 'The number of items per page'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 30
            minimum: 0
            maximum: 100
          style: form
          explode: false
          allowReserved: false
        -
          name: pagination
          in: query
          description: 'Enable or disable pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
        -
          name: partial
          in: query
          description: 'Enable or disable partial pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
      deprecated: false
    post:
      operationId: api_security_events_post
      tags:
        - SecurityEvent
      responses:
        '201':
          description: 'SecurityEvent resource created'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/SecurityEvent.jsonld-security_event.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/SecurityEvent.jsonld-security_event.read'
            application/json:
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
            text/html:
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Creates a SecurityEvent resource.'
      description: 'Creates a SecurityEvent resource.'
      parameters: []
      requestBody:
        description: 'The new SecurityEvent resource'
        content:
          application/ld+json:
            schema:
              $ref: '#/components/schemas/SecurityEvent.jsonld'
          'application/ld+json; version=1.0':
            schema:
              $ref: '#/components/schemas/SecurityEvent.jsonld'
          application/json:
            schema:
              $ref: '#/components/schemas/SecurityEvent'
          'application/json; version=1.0':
            schema:
              $ref: '#/components/schemas/SecurityEvent'
          application/xml:
            schema:
              $ref: '#/components/schemas/SecurityEvent'
          text/xml:
            schema:
              $ref: '#/components/schemas/SecurityEvent'
          'application/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/SecurityEvent'
          'text/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/SecurityEvent'
          text/html:
            schema:
              $ref: '#/components/schemas/SecurityEvent'
          'text/html; version=1.0':
            schema:
              $ref: '#/components/schemas/SecurityEvent'
        required: true
      deprecated: false
  '/api/security_events/{id}':
    get:
      operationId: api_security_events_id_get
      tags:
        - SecurityEvent
      responses:
        '200':
          description: 'SecurityEvent resource'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/SecurityEvent.jsonld-security_event.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/SecurityEvent.jsonld-security_event.read'
            application/json:
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
            text/html:
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves a SecurityEvent resource.'
      description: 'Retrieves a SecurityEvent resource.'
      parameters:
        -
          name: id
          in: path
          description: 'SecurityEvent identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    delete:
      operationId: api_security_events_id_delete
      tags:
        - SecurityEvent
      responses:
        '204':
          description: 'SecurityEvent resource deleted'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Removes the SecurityEvent resource.'
      description: 'Removes the SecurityEvent resource.'
      parameters:
        -
          name: id
          in: path
          description: 'SecurityEvent identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    patch:
      operationId: api_security_events_id_patch
      tags:
        - SecurityEvent
      responses:
        '200':
          description: 'SecurityEvent resource updated'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/SecurityEvent.jsonld-security_event.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/SecurityEvent.jsonld-security_event.read'
            application/json:
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
            text/html:
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/SecurityEvent-security_event.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Updates the SecurityEvent resource.'
      description: 'Updates the SecurityEvent resource.'
      parameters:
        -
          name: id
          in: path
          description: 'SecurityEvent identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      requestBody:
        description: 'The updated SecurityEvent resource'
        content:
          application/merge-patch+json:
            schema:
              $ref: '#/components/schemas/SecurityEvent'
        required: true
      deprecated: false
  /api/system_configurations:
    get:
      operationId: api_system_configurations_get_collection
      tags:
        - SystemConfiguration
      responses:
        '200':
          description: 'SystemConfiguration collection'
          content:
            application/ld+json:
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/SystemConfiguration.jsonld-system_config.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            'application/ld+json; version=1.0':
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/SystemConfiguration.jsonld-system_config.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            'application/json; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            application/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            text/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            'application/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            'text/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            text/html:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            'text/html; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SystemConfiguration-system_config.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves the collection of SystemConfiguration resources.'
      description: 'Retrieves the collection of SystemConfiguration resources.'
      parameters:
        -
          name: page
          in: query
          description: 'The collection page number'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 1
          style: form
          explode: false
          allowReserved: false
        -
          name: itemsPerPage
          in: query
          description: 'The number of items per page'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 30
            minimum: 0
            maximum: 100
          style: form
          explode: false
          allowReserved: false
        -
          name: pagination
          in: query
          description: 'Enable or disable pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
        -
          name: partial
          in: query
          description: 'Enable or disable partial pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
      deprecated: false
    post:
      operationId: api_system_configurations_post
      tags:
        - SystemConfiguration
      responses:
        '201':
          description: 'SystemConfiguration resource created'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/SystemConfiguration.jsonld-system_config.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/SystemConfiguration.jsonld-system_config.read'
            application/json:
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            text/html:
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Creates a SystemConfiguration resource.'
      description: 'Creates a SystemConfiguration resource.'
      parameters: []
      requestBody:
        description: 'The new SystemConfiguration resource'
        content:
          application/ld+json:
            schema:
              $ref: '#/components/schemas/SystemConfiguration.jsonld-system_config.write'
          'application/ld+json; version=1.0':
            schema:
              $ref: '#/components/schemas/SystemConfiguration.jsonld-system_config.write'
          application/json:
            schema:
              $ref: '#/components/schemas/SystemConfiguration-system_config.write'
          'application/json; version=1.0':
            schema:
              $ref: '#/components/schemas/SystemConfiguration-system_config.write'
          application/xml:
            schema:
              $ref: '#/components/schemas/SystemConfiguration-system_config.write'
          text/xml:
            schema:
              $ref: '#/components/schemas/SystemConfiguration-system_config.write'
          'application/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/SystemConfiguration-system_config.write'
          'text/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/SystemConfiguration-system_config.write'
          text/html:
            schema:
              $ref: '#/components/schemas/SystemConfiguration-system_config.write'
          'text/html; version=1.0':
            schema:
              $ref: '#/components/schemas/SystemConfiguration-system_config.write'
        required: true
      deprecated: false
  '/api/system_configurations/{id}':
    get:
      operationId: api_system_configurations_id_get
      tags:
        - SystemConfiguration
      responses:
        '200':
          description: 'SystemConfiguration resource'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/SystemConfiguration.jsonld-system_config.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/SystemConfiguration.jsonld-system_config.read'
            application/json:
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            text/html:
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves a SystemConfiguration resource.'
      description: 'Retrieves a SystemConfiguration resource.'
      parameters:
        -
          name: id
          in: path
          description: 'SystemConfiguration identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    delete:
      operationId: api_system_configurations_id_delete
      tags:
        - SystemConfiguration
      responses:
        '204':
          description: 'SystemConfiguration resource deleted'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Removes the SystemConfiguration resource.'
      description: 'Removes the SystemConfiguration resource.'
      parameters:
        -
          name: id
          in: path
          description: 'SystemConfiguration identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    patch:
      operationId: api_system_configurations_id_patch
      tags:
        - SystemConfiguration
      responses:
        '200':
          description: 'SystemConfiguration resource updated'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/SystemConfiguration.jsonld-system_config.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/SystemConfiguration.jsonld-system_config.read'
            application/json:
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            text/html:
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/SystemConfiguration-system_config.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Updates the SystemConfiguration resource.'
      description: 'Updates the SystemConfiguration resource.'
      parameters:
        -
          name: id
          in: path
          description: 'SystemConfiguration identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      requestBody:
        description: 'The updated SystemConfiguration resource'
        content:
          application/merge-patch+json:
            schema:
              $ref: '#/components/schemas/SystemConfiguration-system_config.write'
        required: true
      deprecated: false
  /api/users:
    get:
      operationId: api_users_get_collection
      tags:
        - User
      responses:
        '200':
          description: 'User collection'
          content:
            application/ld+json:
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/User.jsonld-user.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            'application/ld+json; version=1.0':
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/User.jsonld-user.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User-user.read'
            'application/json; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User-user.read'
            application/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User-user.read'
            text/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User-user.read'
            'application/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User-user.read'
            'text/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User-user.read'
            text/html:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User-user.read'
            'text/html; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User-user.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves the collection of User resources.'
      description: 'Retrieves the collection of User resources.'
      parameters:
        -
          name: page
          in: query
          description: 'The collection page number'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 1
          style: form
          explode: false
          allowReserved: false
        -
          name: itemsPerPage
          in: query
          description: 'The number of items per page'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 30
            minimum: 0
            maximum: 100
          style: form
          explode: false
          allowReserved: false
        -
          name: pagination
          in: query
          description: 'Enable or disable pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
        -
          name: partial
          in: query
          description: 'Enable or disable partial pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
      deprecated: false
    post:
      operationId: api_users_post
      tags:
        - User
      responses:
        '201':
          description: 'User resource created'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/User.jsonld-user.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/User.jsonld-user.read'
            application/json:
              schema:
                $ref: '#/components/schemas/User-user.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/User-user.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/User-user.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/User-user.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/User-user.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/User-user.read'
            text/html:
              schema:
                $ref: '#/components/schemas/User-user.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/User-user.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Creates a User resource.'
      description: 'Creates a User resource.'
      parameters: []
      requestBody:
        description: 'The new User resource'
        content:
          application/ld+json:
            schema:
              $ref: '#/components/schemas/User.jsonld-user.write'
          'application/ld+json; version=1.0':
            schema:
              $ref: '#/components/schemas/User.jsonld-user.write'
          application/json:
            schema:
              $ref: '#/components/schemas/User-user.write'
          'application/json; version=1.0':
            schema:
              $ref: '#/components/schemas/User-user.write'
          application/xml:
            schema:
              $ref: '#/components/schemas/User-user.write'
          text/xml:
            schema:
              $ref: '#/components/schemas/User-user.write'
          'application/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/User-user.write'
          'text/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/User-user.write'
          text/html:
            schema:
              $ref: '#/components/schemas/User-user.write'
          'text/html; version=1.0':
            schema:
              $ref: '#/components/schemas/User-user.write'
        required: true
      deprecated: false
  '/api/users/{id}':
    get:
      operationId: api_users_id_get
      tags:
        - User
      responses:
        '200':
          description: 'User resource'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/User.jsonld-user.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/User.jsonld-user.read'
            application/json:
              schema:
                $ref: '#/components/schemas/User-user.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/User-user.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/User-user.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/User-user.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/User-user.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/User-user.read'
            text/html:
              schema:
                $ref: '#/components/schemas/User-user.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/User-user.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves a User resource.'
      description: 'Retrieves a User resource.'
      parameters:
        -
          name: id
          in: path
          description: 'User identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    delete:
      operationId: api_users_id_delete
      tags:
        - User
      responses:
        '204':
          description: 'User resource deleted'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Removes the User resource.'
      description: 'Removes the User resource.'
      parameters:
        -
          name: id
          in: path
          description: 'User identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    patch:
      operationId: api_users_id_patch
      tags:
        - User
      responses:
        '200':
          description: 'User resource updated'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/User.jsonld-user.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/User.jsonld-user.read'
            application/json:
              schema:
                $ref: '#/components/schemas/User-user.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/User-user.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/User-user.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/User-user.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/User-user.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/User-user.read'
            text/html:
              schema:
                $ref: '#/components/schemas/User-user.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/User-user.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Updates the User resource.'
      description: 'Updates the User resource.'
      parameters:
        -
          name: id
          in: path
          description: 'User identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      requestBody:
        description: 'The updated User resource'
        content:
          application/merge-patch+json:
            schema:
              $ref: '#/components/schemas/User-user.write'
        required: true
      deprecated: false
  /api/user2_f_a_settings:
    get:
      operationId: api_user2_f_a_settings_get_collection
      tags:
        - User2FASetting
      responses:
        '200':
          description: 'User2FASetting collection'
          content:
            application/ld+json:
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/User2FASetting.jsonld-user_2fa.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            'application/ld+json; version=1.0':
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/User2FASetting.jsonld-user_2fa.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            'application/json; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            application/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            text/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            'application/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            'text/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            text/html:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            'text/html; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User2FASetting-user_2fa.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves the collection of User2FASetting resources.'
      description: 'Retrieves the collection of User2FASetting resources.'
      parameters:
        -
          name: page
          in: query
          description: 'The collection page number'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 1
          style: form
          explode: false
          allowReserved: false
        -
          name: itemsPerPage
          in: query
          description: 'The number of items per page'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 30
            minimum: 0
            maximum: 100
          style: form
          explode: false
          allowReserved: false
        -
          name: pagination
          in: query
          description: 'Enable or disable pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
        -
          name: partial
          in: query
          description: 'Enable or disable partial pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
      deprecated: false
    post:
      operationId: api_user2_f_a_settings_post
      tags:
        - User2FASetting
      responses:
        '201':
          description: 'User2FASetting resource created'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/User2FASetting.jsonld-user_2fa.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/User2FASetting.jsonld-user_2fa.read'
            application/json:
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            text/html:
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Creates a User2FASetting resource.'
      description: 'Creates a User2FASetting resource.'
      parameters: []
      requestBody:
        description: 'The new User2FASetting resource'
        content:
          application/ld+json:
            schema:
              $ref: '#/components/schemas/User2FASetting.jsonld-user_2fa.write'
          'application/ld+json; version=1.0':
            schema:
              $ref: '#/components/schemas/User2FASetting.jsonld-user_2fa.write'
          application/json:
            schema:
              $ref: '#/components/schemas/User2FASetting-user_2fa.write'
          'application/json; version=1.0':
            schema:
              $ref: '#/components/schemas/User2FASetting-user_2fa.write'
          application/xml:
            schema:
              $ref: '#/components/schemas/User2FASetting-user_2fa.write'
          text/xml:
            schema:
              $ref: '#/components/schemas/User2FASetting-user_2fa.write'
          'application/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/User2FASetting-user_2fa.write'
          'text/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/User2FASetting-user_2fa.write'
          text/html:
            schema:
              $ref: '#/components/schemas/User2FASetting-user_2fa.write'
          'text/html; version=1.0':
            schema:
              $ref: '#/components/schemas/User2FASetting-user_2fa.write'
        required: true
      deprecated: false
  '/api/user2_f_a_settings/{id}':
    get:
      operationId: api_user2_f_a_settings_id_get
      tags:
        - User2FASetting
      responses:
        '200':
          description: 'User2FASetting resource'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/User2FASetting.jsonld-user_2fa.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/User2FASetting.jsonld-user_2fa.read'
            application/json:
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            text/html:
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves a User2FASetting resource.'
      description: 'Retrieves a User2FASetting resource.'
      parameters:
        -
          name: id
          in: path
          description: 'User2FASetting identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    delete:
      operationId: api_user2_f_a_settings_id_delete
      tags:
        - User2FASetting
      responses:
        '204':
          description: 'User2FASetting resource deleted'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Removes the User2FASetting resource.'
      description: 'Removes the User2FASetting resource.'
      parameters:
        -
          name: id
          in: path
          description: 'User2FASetting identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    patch:
      operationId: api_user2_f_a_settings_id_patch
      tags:
        - User2FASetting
      responses:
        '200':
          description: 'User2FASetting resource updated'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/User2FASetting.jsonld-user_2fa.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/User2FASetting.jsonld-user_2fa.read'
            application/json:
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            text/html:
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/User2FASetting-user_2fa.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Updates the User2FASetting resource.'
      description: 'Updates the User2FASetting resource.'
      parameters:
        -
          name: id
          in: path
          description: 'User2FASetting identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      requestBody:
        description: 'The updated User2FASetting resource'
        content:
          application/merge-patch+json:
            schema:
              $ref: '#/components/schemas/User2FASetting-user_2fa.write'
        required: true
      deprecated: false
  /api/user_notifications:
    get:
      operationId: api_user_notifications_get_collection
      tags:
        - UserNotification
      responses:
        '200':
          description: 'UserNotification collection'
          content:
            application/ld+json:
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/UserNotification.jsonld-notification.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            'application/ld+json; version=1.0':
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/UserNotification.jsonld-notification.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserNotification-notification.read'
            'application/json; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserNotification-notification.read'
            application/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserNotification-notification.read'
            text/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserNotification-notification.read'
            'application/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserNotification-notification.read'
            'text/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserNotification-notification.read'
            text/html:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserNotification-notification.read'
            'text/html; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserNotification-notification.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves the collection of UserNotification resources.'
      description: 'Retrieves the collection of UserNotification resources.'
      parameters:
        -
          name: page
          in: query
          description: 'The collection page number'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 1
          style: form
          explode: false
          allowReserved: false
        -
          name: itemsPerPage
          in: query
          description: 'The number of items per page'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 30
            minimum: 0
            maximum: 100
          style: form
          explode: false
          allowReserved: false
        -
          name: pagination
          in: query
          description: 'Enable or disable pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
        -
          name: partial
          in: query
          description: 'Enable or disable partial pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
      deprecated: false
    post:
      operationId: api_user_notifications_post
      tags:
        - UserNotification
      responses:
        '201':
          description: 'UserNotification resource created'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/UserNotification.jsonld-notification.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserNotification.jsonld-notification.read'
            application/json:
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
            text/html:
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Creates a UserNotification resource.'
      description: 'Creates a UserNotification resource.'
      parameters: []
      requestBody:
        description: 'The new UserNotification resource'
        content:
          application/ld+json:
            schema:
              $ref: '#/components/schemas/UserNotification.jsonld-notification.write'
          'application/ld+json; version=1.0':
            schema:
              $ref: '#/components/schemas/UserNotification.jsonld-notification.write'
          application/json:
            schema:
              $ref: '#/components/schemas/UserNotification-notification.write'
          'application/json; version=1.0':
            schema:
              $ref: '#/components/schemas/UserNotification-notification.write'
          application/xml:
            schema:
              $ref: '#/components/schemas/UserNotification-notification.write'
          text/xml:
            schema:
              $ref: '#/components/schemas/UserNotification-notification.write'
          'application/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/UserNotification-notification.write'
          'text/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/UserNotification-notification.write'
          text/html:
            schema:
              $ref: '#/components/schemas/UserNotification-notification.write'
          'text/html; version=1.0':
            schema:
              $ref: '#/components/schemas/UserNotification-notification.write'
        required: true
      deprecated: false
  '/api/user_notifications/{id}':
    get:
      operationId: api_user_notifications_id_get
      tags:
        - UserNotification
      responses:
        '200':
          description: 'UserNotification resource'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/UserNotification.jsonld-notification.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserNotification.jsonld-notification.read'
            application/json:
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
            text/html:
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves a UserNotification resource.'
      description: 'Retrieves a UserNotification resource.'
      parameters:
        -
          name: id
          in: path
          description: 'UserNotification identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    delete:
      operationId: api_user_notifications_id_delete
      tags:
        - UserNotification
      responses:
        '204':
          description: 'UserNotification resource deleted'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Removes the UserNotification resource.'
      description: 'Removes the UserNotification resource.'
      parameters:
        -
          name: id
          in: path
          description: 'UserNotification identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    patch:
      operationId: api_user_notifications_id_patch
      tags:
        - UserNotification
      responses:
        '200':
          description: 'UserNotification resource updated'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/UserNotification.jsonld-notification.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserNotification.jsonld-notification.read'
            application/json:
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
            text/html:
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/UserNotification-notification.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Updates the UserNotification resource.'
      description: 'Updates the UserNotification resource.'
      parameters:
        -
          name: id
          in: path
          description: 'UserNotification identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      requestBody:
        description: 'The updated UserNotification resource'
        content:
          application/merge-patch+json:
            schema:
              $ref: '#/components/schemas/UserNotification-notification.write'
        required: true
      deprecated: false
  /api/user_profiles:
    get:
      operationId: api_user_profiles_get_collection
      tags:
        - UserProfile
      responses:
        '200':
          description: 'UserProfile collection'
          content:
            application/ld+json:
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/UserProfile.jsonld-user_profile.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            'application/ld+json; version=1.0':
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/UserProfile.jsonld-user_profile.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserProfile-user_profile.read'
            'application/json; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserProfile-user_profile.read'
            application/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserProfile-user_profile.read'
            text/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserProfile-user_profile.read'
            'application/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserProfile-user_profile.read'
            'text/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserProfile-user_profile.read'
            text/html:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserProfile-user_profile.read'
            'text/html; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserProfile-user_profile.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves the collection of UserProfile resources.'
      description: 'Retrieves the collection of UserProfile resources.'
      parameters:
        -
          name: page
          in: query
          description: 'The collection page number'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 1
          style: form
          explode: false
          allowReserved: false
        -
          name: itemsPerPage
          in: query
          description: 'The number of items per page'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 30
            minimum: 0
            maximum: 100
          style: form
          explode: false
          allowReserved: false
        -
          name: pagination
          in: query
          description: 'Enable or disable pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
        -
          name: partial
          in: query
          description: 'Enable or disable partial pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
      deprecated: false
    post:
      operationId: api_user_profiles_post
      tags:
        - UserProfile
      responses:
        '201':
          description: 'UserProfile resource created'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/UserProfile.jsonld-user_profile.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserProfile.jsonld-user_profile.read'
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
            text/html:
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Creates a UserProfile resource.'
      description: 'Creates a UserProfile resource.'
      parameters: []
      requestBody:
        description: 'The new UserProfile resource'
        content:
          application/ld+json:
            schema:
              $ref: '#/components/schemas/UserProfile.jsonld-user_profile.write'
          'application/ld+json; version=1.0':
            schema:
              $ref: '#/components/schemas/UserProfile.jsonld-user_profile.write'
          application/json:
            schema:
              $ref: '#/components/schemas/UserProfile-user_profile.write'
          'application/json; version=1.0':
            schema:
              $ref: '#/components/schemas/UserProfile-user_profile.write'
          application/xml:
            schema:
              $ref: '#/components/schemas/UserProfile-user_profile.write'
          text/xml:
            schema:
              $ref: '#/components/schemas/UserProfile-user_profile.write'
          'application/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/UserProfile-user_profile.write'
          'text/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/UserProfile-user_profile.write'
          text/html:
            schema:
              $ref: '#/components/schemas/UserProfile-user_profile.write'
          'text/html; version=1.0':
            schema:
              $ref: '#/components/schemas/UserProfile-user_profile.write'
        required: true
      deprecated: false
  '/api/user_profiles/{id}':
    get:
      operationId: api_user_profiles_id_get
      tags:
        - UserProfile
      responses:
        '200':
          description: 'UserProfile resource'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/UserProfile.jsonld-user_profile.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserProfile.jsonld-user_profile.read'
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
            text/html:
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves a UserProfile resource.'
      description: 'Retrieves a UserProfile resource.'
      parameters:
        -
          name: id
          in: path
          description: 'UserProfile identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    delete:
      operationId: api_user_profiles_id_delete
      tags:
        - UserProfile
      responses:
        '204':
          description: 'UserProfile resource deleted'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Removes the UserProfile resource.'
      description: 'Removes the UserProfile resource.'
      parameters:
        -
          name: id
          in: path
          description: 'UserProfile identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    patch:
      operationId: api_user_profiles_id_patch
      tags:
        - UserProfile
      responses:
        '200':
          description: 'UserProfile resource updated'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/UserProfile.jsonld-user_profile.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserProfile.jsonld-user_profile.read'
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
            text/html:
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/UserProfile-user_profile.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Updates the UserProfile resource.'
      description: 'Updates the UserProfile resource.'
      parameters:
        -
          name: id
          in: path
          description: 'UserProfile identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      requestBody:
        description: 'The updated UserProfile resource'
        content:
          application/merge-patch+json:
            schema:
              $ref: '#/components/schemas/UserProfile-user_profile.write'
        required: true
      deprecated: false
  /api/user_roles:
    get:
      operationId: api_user_roles_get_collection
      tags:
        - UserRole
      responses:
        '200':
          description: 'UserRole collection'
          content:
            application/ld+json:
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/UserRole.jsonld-user_role.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            'application/ld+json; version=1.0':
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/UserRole.jsonld-user_role.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserRole-user_role.read'
            'application/json; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserRole-user_role.read'
            application/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserRole-user_role.read'
            text/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserRole-user_role.read'
            'application/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserRole-user_role.read'
            'text/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserRole-user_role.read'
            text/html:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserRole-user_role.read'
            'text/html; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserRole-user_role.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves the collection of UserRole resources.'
      description: 'Retrieves the collection of UserRole resources.'
      parameters:
        -
          name: page
          in: query
          description: 'The collection page number'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 1
          style: form
          explode: false
          allowReserved: false
        -
          name: itemsPerPage
          in: query
          description: 'The number of items per page'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 30
            minimum: 0
            maximum: 100
          style: form
          explode: false
          allowReserved: false
        -
          name: pagination
          in: query
          description: 'Enable or disable pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
        -
          name: partial
          in: query
          description: 'Enable or disable partial pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
      deprecated: false
    post:
      operationId: api_user_roles_post
      tags:
        - UserRole
      responses:
        '201':
          description: 'UserRole resource created'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/UserRole.jsonld-user_role.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserRole.jsonld-user_role.read'
            application/json:
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
            text/html:
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Creates a UserRole resource.'
      description: 'Creates a UserRole resource.'
      parameters: []
      requestBody:
        description: 'The new UserRole resource'
        content:
          application/ld+json:
            schema:
              $ref: '#/components/schemas/UserRole.jsonld-user_role.write'
          'application/ld+json; version=1.0':
            schema:
              $ref: '#/components/schemas/UserRole.jsonld-user_role.write'
          application/json:
            schema:
              $ref: '#/components/schemas/UserRole-user_role.write'
          'application/json; version=1.0':
            schema:
              $ref: '#/components/schemas/UserRole-user_role.write'
          application/xml:
            schema:
              $ref: '#/components/schemas/UserRole-user_role.write'
          text/xml:
            schema:
              $ref: '#/components/schemas/UserRole-user_role.write'
          'application/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/UserRole-user_role.write'
          'text/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/UserRole-user_role.write'
          text/html:
            schema:
              $ref: '#/components/schemas/UserRole-user_role.write'
          'text/html; version=1.0':
            schema:
              $ref: '#/components/schemas/UserRole-user_role.write'
        required: true
      deprecated: false
  '/api/user_roles/{id}':
    get:
      operationId: api_user_roles_id_get
      tags:
        - UserRole
      responses:
        '200':
          description: 'UserRole resource'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/UserRole.jsonld-user_role.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserRole.jsonld-user_role.read'
            application/json:
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
            text/html:
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves a UserRole resource.'
      description: 'Retrieves a UserRole resource.'
      parameters:
        -
          name: id
          in: path
          description: 'UserRole identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    delete:
      operationId: api_user_roles_id_delete
      tags:
        - UserRole
      responses:
        '204':
          description: 'UserRole resource deleted'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Removes the UserRole resource.'
      description: 'Removes the UserRole resource.'
      parameters:
        -
          name: id
          in: path
          description: 'UserRole identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    patch:
      operationId: api_user_roles_id_patch
      tags:
        - UserRole
      responses:
        '200':
          description: 'UserRole resource updated'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/UserRole.jsonld-user_role.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserRole.jsonld-user_role.read'
            application/json:
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
            text/html:
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/UserRole-user_role.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Updates the UserRole resource.'
      description: 'Updates the UserRole resource.'
      parameters:
        -
          name: id
          in: path
          description: 'UserRole identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      requestBody:
        description: 'The updated UserRole resource'
        content:
          application/merge-patch+json:
            schema:
              $ref: '#/components/schemas/UserRole-user_role.write'
        required: true
      deprecated: false
  /api/user_verifications:
    get:
      operationId: api_user_verifications_get_collection
      tags:
        - UserVerification
      responses:
        '200':
          description: 'UserVerification collection'
          content:
            application/ld+json:
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/UserVerification.jsonld-user_verification.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            'application/ld+json; version=1.0':
              schema:
                type: object
                properties:
                  member: { type: array, items: { $ref: '#/components/schemas/UserVerification.jsonld-user_verification.read' } }
                  totalItems: { type: integer, minimum: 0 }
                  view: { type: object, properties: { '@id': { type: string, format: iri-reference }, '@type': { type: string }, first: { type: string, format: iri-reference }, last: { type: string, format: iri-reference }, previous: { type: string, format: iri-reference }, next: { type: string, format: iri-reference } }, example: { '@id': string, type: string, first: string, last: string, previous: string, next: string } }
                  search: { type: object, properties: { '@type': { type: string }, template: { type: string }, variableRepresentation: { type: string }, mapping: { type: array, items: { type: object, properties: { '@type': { type: string }, variable: { type: string }, property: { type: [string, 'null'] }, required: { type: boolean } } } } } }
                required:
                  - member
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserVerification-user_verification.read'
            'application/json; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserVerification-user_verification.read'
            application/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserVerification-user_verification.read'
            text/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserVerification-user_verification.read'
            'application/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserVerification-user_verification.read'
            'text/xml; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserVerification-user_verification.read'
            text/html:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserVerification-user_verification.read'
            'text/html; version=1.0':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserVerification-user_verification.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves the collection of UserVerification resources.'
      description: 'Retrieves the collection of UserVerification resources.'
      parameters:
        -
          name: page
          in: query
          description: 'The collection page number'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 1
          style: form
          explode: false
          allowReserved: false
        -
          name: itemsPerPage
          in: query
          description: 'The number of items per page'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: integer
            default: 30
            minimum: 0
            maximum: 100
          style: form
          explode: false
          allowReserved: false
        -
          name: pagination
          in: query
          description: 'Enable or disable pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
        -
          name: partial
          in: query
          description: 'Enable or disable partial pagination'
          required: false
          deprecated: false
          allowEmptyValue: true
          schema:
            type: boolean
          style: form
          explode: false
          allowReserved: false
      deprecated: false
    post:
      operationId: api_user_verifications_post
      tags:
        - UserVerification
      responses:
        '201':
          description: 'UserVerification resource created'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/UserVerification.jsonld-user_verification.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserVerification.jsonld-user_verification.read'
            application/json:
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
            text/html:
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Creates a UserVerification resource.'
      description: 'Creates a UserVerification resource.'
      parameters: []
      requestBody:
        description: 'The new UserVerification resource'
        content:
          application/ld+json:
            schema:
              $ref: '#/components/schemas/UserVerification.jsonld-user_verification.write'
          'application/ld+json; version=1.0':
            schema:
              $ref: '#/components/schemas/UserVerification.jsonld-user_verification.write'
          application/json:
            schema:
              $ref: '#/components/schemas/UserVerification-user_verification.write'
          'application/json; version=1.0':
            schema:
              $ref: '#/components/schemas/UserVerification-user_verification.write'
          application/xml:
            schema:
              $ref: '#/components/schemas/UserVerification-user_verification.write'
          text/xml:
            schema:
              $ref: '#/components/schemas/UserVerification-user_verification.write'
          'application/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/UserVerification-user_verification.write'
          'text/xml; version=1.0':
            schema:
              $ref: '#/components/schemas/UserVerification-user_verification.write'
          text/html:
            schema:
              $ref: '#/components/schemas/UserVerification-user_verification.write'
          'text/html; version=1.0':
            schema:
              $ref: '#/components/schemas/UserVerification-user_verification.write'
        required: true
      deprecated: false
  '/api/user_verifications/{id}':
    get:
      operationId: api_user_verifications_id_get
      tags:
        - UserVerification
      responses:
        '200':
          description: 'UserVerification resource'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/UserVerification.jsonld-user_verification.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserVerification.jsonld-user_verification.read'
            application/json:
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
            text/html:
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Retrieves a UserVerification resource.'
      description: 'Retrieves a UserVerification resource.'
      parameters:
        -
          name: id
          in: path
          description: 'UserVerification identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    delete:
      operationId: api_user_verifications_id_delete
      tags:
        - UserVerification
      responses:
        '204':
          description: 'UserVerification resource deleted'
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Removes the UserVerification resource.'
      description: 'Removes the UserVerification resource.'
      parameters:
        -
          name: id
          in: path
          description: 'UserVerification identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      deprecated: false
    patch:
      operationId: api_user_verifications_id_patch
      tags:
        - UserVerification
      responses:
        '200':
          description: 'UserVerification resource updated'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/UserVerification.jsonld-user_verification.read'
            'application/ld+json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserVerification.jsonld-user_verification.read'
            application/json:
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
            'application/json; version=1.0':
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
            application/xml:
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
            text/xml:
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
            'application/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
            'text/xml; version=1.0':
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
            text/html:
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
            'text/html; version=1.0':
              schema:
                $ref: '#/components/schemas/UserVerification-user_verification.read'
          links: {  }
        '400':
          description: 'Invalid input'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '422':
          description: 'An error occurred'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation.jsonld-jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
            application/json:
              schema:
                $ref: '#/components/schemas/ConstraintViolation-json'
          links: {  }
        '403':
          description: Forbidden
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
        '404':
          description: 'Not found'
          content:
            application/ld+json:
              schema:
                $ref: '#/components/schemas/Error.jsonld'
            application/problem+json:
              schema:
                $ref: '#/components/schemas/Error'
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
          links: {  }
      summary: 'Updates the UserVerification resource.'
      description: 'Updates the UserVerification resource.'
      parameters:
        -
          name: id
          in: path
          description: 'UserVerification identifier'
          required: true
          deprecated: false
          allowEmptyValue: false
          schema:
            type: string
          style: simple
          explode: false
          allowReserved: false
      requestBody:
        description: 'The updated UserVerification resource'
        content:
          application/merge-patch+json:
            schema:
              $ref: '#/components/schemas/UserVerification-user_verification.write'
        required: true
      deprecated: false
components:
  schemas:
    AuditLog:
      type: object
      description: ''
      deprecated: false
      properties:
        id:
          readOnly: true
          type: string
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        action:
          type: string
        category:
          type: string
        resourceType:
          type: string
        resourceId:
          type:
            - integer
            - 'null'
        oldValues:
          type:
            - array
            - 'null'
          items:
            type: string
        newValues:
          type:
            - array
            - 'null'
          items:
            type: string
        ipAddress:
          type:
            - string
            - 'null'
        userAgent:
          type:
            - string
            - 'null'
        sessionId:
          type:
            - string
            - 'null'
        source:
          type:
            - string
            - 'null'
        success:
          default: true
          example: true
          type: boolean
        timestamp:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
    AuditLog-audit_log.read:
      type: object
      description: ''
      deprecated: false
      properties:
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        action:
          type: string
        category:
          type: string
        resourceType:
          type: string
        resourceId:
          type:
            - integer
            - 'null'
        oldValues:
          type:
            - array
            - 'null'
          items:
            type: string
        newValues:
          type:
            - array
            - 'null'
          items:
            type: string
        ipAddress:
          type:
            - string
            - 'null'
        userAgent:
          type:
            - string
            - 'null'
        sessionId:
          type:
            - string
            - 'null'
        source:
          type:
            - string
            - 'null'
        success:
          default: true
          example: true
          type: boolean
        timestamp:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
    AuditLog.jsonld:
      type: object
      description: ''
      deprecated: false
      properties:
        id:
          readOnly: true
          type: string
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        action:
          type: string
        category:
          type: string
        resourceType:
          type: string
        resourceId:
          type:
            - integer
            - 'null'
        oldValues:
          type:
            - array
            - 'null'
          items:
            type: string
        newValues:
          type:
            - array
            - 'null'
          items:
            type: string
        ipAddress:
          type:
            - string
            - 'null'
        userAgent:
          type:
            - string
            - 'null'
        sessionId:
          type:
            - string
            - 'null'
        source:
          type:
            - string
            - 'null'
        success:
          default: true
          example: true
          type: boolean
        timestamp:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
    AuditLog.jsonld-audit_log.read:
      type: object
      description: ''
      deprecated: false
      properties:
        '@context':
          readOnly: true
          oneOf:
            -
              type: string
            -
              type: object
              properties:
                '@vocab':
                  type: string
                hydra:
                  type: string
                  enum: ['http://www.w3.org/ns/hydra/core#']
              required:
                - '@vocab'
                - hydra
              additionalProperties: true
        '@id':
          readOnly: true
          type: string
        '@type':
          readOnly: true
          type: string
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        action:
          type: string
        category:
          type: string
        resourceType:
          type: string
        resourceId:
          type:
            - integer
            - 'null'
        oldValues:
          type:
            - array
            - 'null'
          items:
            type: string
        newValues:
          type:
            - array
            - 'null'
          items:
            type: string
        ipAddress:
          type:
            - string
            - 'null'
        userAgent:
          type:
            - string
            - 'null'
        sessionId:
          type:
            - string
            - 'null'
        source:
          type:
            - string
            - 'null'
        success:
          default: true
          example: true
          type: boolean
        timestamp:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
    ConfigurationHistory:
      type: object
      description: ''
      deprecated: false
      properties:
        id:
          readOnly: true
          type: string
        configKey:
          type: string
        oldValue:
          type:
            - string
            - 'null'
        newValue:
          type:
            - string
            - 'null'
        changedBy:
          type: string
          format: iri-reference
          example: 'https://example.com/'
        changeReason:
          type:
            - string
            - 'null'
        ipAddress:
          type:
            - string
            - 'null'
        userAgent:
          type:
            - string
            - 'null'
        timestamp:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
    ConfigurationHistory-config_history.read:
      type: object
      description: ''
      deprecated: false
      properties:
        configKey:
          type: string
        oldValue:
          type:
            - string
            - 'null'
        newValue:
          type:
            - string
            - 'null'
        changedBy:
          type: string
          format: iri-reference
          example: 'https://example.com/'
        changeReason:
          type:
            - string
            - 'null'
        ipAddress:
          type:
            - string
            - 'null'
        userAgent:
          type:
            - string
            - 'null'
        timestamp:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
    ConfigurationHistory.jsonld:
      type: object
      description: ''
      deprecated: false
      properties:
        id:
          readOnly: true
          type: string
        configKey:
          type: string
        oldValue:
          type:
            - string
            - 'null'
        newValue:
          type:
            - string
            - 'null'
        changedBy:
          type: string
          format: iri-reference
          example: 'https://example.com/'
        changeReason:
          type:
            - string
            - 'null'
        ipAddress:
          type:
            - string
            - 'null'
        userAgent:
          type:
            - string
            - 'null'
        timestamp:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
    ConfigurationHistory.jsonld-config_history.read:
      type: object
      description: ''
      deprecated: false
      properties:
        '@context':
          readOnly: true
          oneOf:
            -
              type: string
            -
              type: object
              properties:
                '@vocab':
                  type: string
                hydra:
                  type: string
                  enum: ['http://www.w3.org/ns/hydra/core#']
              required:
                - '@vocab'
                - hydra
              additionalProperties: true
        '@id':
          readOnly: true
          type: string
        '@type':
          readOnly: true
          type: string
        configKey:
          type: string
        oldValue:
          type:
            - string
            - 'null'
        newValue:
          type:
            - string
            - 'null'
        changedBy:
          type: string
          format: iri-reference
          example: 'https://example.com/'
        changeReason:
          type:
            - string
            - 'null'
        ipAddress:
          type:
            - string
            - 'null'
        userAgent:
          type:
            - string
            - 'null'
        timestamp:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
    ConstraintViolation-json:
      type: object
      description: 'Unprocessable entity'
      deprecated: false
      properties:
        status:
          default: 422
          example: 422
          type: integer
        violations:
          type: array
          items:
            type: object
            properties:
              propertyPath:
                type: string
                description: 'The property path of the violation'
              message:
                type: string
                description: 'The message associated with the violation'
        detail:
          readOnly: true
          type: string
        type:
          readOnly: true
          type: string
        title:
          readOnly: true
          type:
            - string
            - 'null'
        instance:
          readOnly: true
          type:
            - string
            - 'null'
    ConstraintViolation.jsonld-jsonld:
      type: object
      description: 'Unprocessable entity'
      deprecated: false
      properties:
        '@context':
          readOnly: true
          oneOf:
            -
              type: string
            -
              type: object
              properties:
                '@vocab':
                  type: string
                hydra:
                  type: string
                  enum: ['http://www.w3.org/ns/hydra/core#']
              required:
                - '@vocab'
                - hydra
              additionalProperties: true
        '@id':
          readOnly: true
          type: string
        '@type':
          readOnly: true
          type: string
        status:
          default: 422
          example: 422
          type: integer
        violations:
          type: array
          items:
            type: object
            properties:
              propertyPath:
                type: string
                description: 'The property path of the violation'
              message:
                type: string
                description: 'The message associated with the violation'
        detail:
          readOnly: true
          type: string
        description:
          readOnly: true
          type: string
        type:
          readOnly: true
          type: string
        title:
          readOnly: true
          type:
            - string
            - 'null'
        instance:
          readOnly: true
          type:
            - string
            - 'null'
    Doctor-doctor.read:
      type: object
      description: ''
      deprecated: false
      properties:
        prefix:
          type:
            - string
            - 'null'
        firstName:
          type: string
        lastName:
          type: string
        suffix:
          type:
            - string
            - 'null'
        registrationNumber:
          type: string
        registrationExpiryDate:
          type:
            - string
            - 'null'
          format: date-time
        education:
          type:
            - array
            - 'null'
          items:
            type: string
        degree:
          type: string
        specialization:
          type: string
        yearsOfExperience:
          type:
            - integer
            - 'null'
        bio:
          type:
            - string
            - 'null'
        picture:
          type:
            - string
            - 'null'
        consultationFee:
          type:
            - string
            - 'null'
        availabilitySchedule:
          type:
            - array
            - 'null'
          items:
            type: string
        status:
          default: active
          example: active
          type: string
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    Doctor-doctor.write:
      type: object
      description: ''
      deprecated: false
      properties:
        prefix:
          type:
            - string
            - 'null'
        firstName:
          type: string
        lastName:
          type: string
        suffix:
          type:
            - string
            - 'null'
        registrationNumber:
          type: string
        registrationExpiryDate:
          type:
            - string
            - 'null'
          format: date-time
        education:
          type:
            - array
            - 'null'
          items:
            type: string
        degree:
          type: string
        specialization:
          type: string
        yearsOfExperience:
          type:
            - integer
            - 'null'
        bio:
          type:
            - string
            - 'null'
        picture:
          type:
            - string
            - 'null'
        consultationFee:
          type:
            - string
            - 'null'
        availabilitySchedule:
          type:
            - array
            - 'null'
          items:
            type: string
        status:
          default: active
          example: active
          type: string
    Doctor.jsonld-doctor.read:
      type: object
      description: ''
      deprecated: false
      properties:
        '@context':
          readOnly: true
          oneOf:
            -
              type: string
            -
              type: object
              properties:
                '@vocab':
                  type: string
                hydra:
                  type: string
                  enum: ['http://www.w3.org/ns/hydra/core#']
              required:
                - '@vocab'
                - hydra
              additionalProperties: true
        '@id':
          readOnly: true
          type: string
        '@type':
          readOnly: true
          type: string
        prefix:
          type:
            - string
            - 'null'
        firstName:
          type: string
        lastName:
          type: string
        suffix:
          type:
            - string
            - 'null'
        registrationNumber:
          type: string
        registrationExpiryDate:
          type:
            - string
            - 'null'
          format: date-time
        education:
          type:
            - array
            - 'null'
          items:
            type: string
        degree:
          type: string
        specialization:
          type: string
        yearsOfExperience:
          type:
            - integer
            - 'null'
        bio:
          type:
            - string
            - 'null'
        picture:
          type:
            - string
            - 'null'
        consultationFee:
          type:
            - string
            - 'null'
        availabilitySchedule:
          type:
            - array
            - 'null'
          items:
            type: string
        status:
          default: active
          example: active
          type: string
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    Doctor.jsonld-doctor.write:
      type: object
      description: ''
      deprecated: false
      properties:
        prefix:
          type:
            - string
            - 'null'
        firstName:
          type: string
        lastName:
          type: string
        suffix:
          type:
            - string
            - 'null'
        registrationNumber:
          type: string
        registrationExpiryDate:
          type:
            - string
            - 'null'
          format: date-time
        education:
          type:
            - array
            - 'null'
          items:
            type: string
        degree:
          type: string
        specialization:
          type: string
        yearsOfExperience:
          type:
            - integer
            - 'null'
        bio:
          type:
            - string
            - 'null'
        picture:
          type:
            - string
            - 'null'
        consultationFee:
          type:
            - string
            - 'null'
        availabilitySchedule:
          type:
            - array
            - 'null'
          items:
            type: string
        status:
          default: active
          example: active
          type: string
    Error:
      type: object
      description: 'A representation of common errors.'
      deprecated: false
      properties:
        title:
          readOnly: true
          description: 'A short, human-readable summary of the problem.'
          type: string
        detail:
          readOnly: true
          description: 'A human-readable explanation specific to this occurrence of the problem.'
          type: string
        status:
          type: number
          examples:
            - 404
          default: 400
        instance:
          readOnly: true
          description: 'A URI reference that identifies the specific occurrence of the problem. It may or may not yield further information if dereferenced.'
          type:
            - string
            - 'null'
        type:
          readOnly: true
          description: 'A URI reference that identifies the problem type'
          type: string
    Error.jsonld:
      type: object
      description: 'A representation of common errors.'
      deprecated: false
      properties:
        '@context':
          readOnly: true
          oneOf:
            -
              type: string
            -
              type: object
              properties:
                '@vocab':
                  type: string
                hydra:
                  type: string
                  enum: ['http://www.w3.org/ns/hydra/core#']
              required:
                - '@vocab'
                - hydra
              additionalProperties: true
        '@id':
          readOnly: true
          type: string
        '@type':
          readOnly: true
          type: string
        title:
          readOnly: true
          description: 'A short, human-readable summary of the problem.'
          type: string
        detail:
          readOnly: true
          description: 'A human-readable explanation specific to this occurrence of the problem.'
          type: string
        status:
          type: number
          examples:
            - 404
          default: 400
        instance:
          readOnly: true
          description: 'A URI reference that identifies the specific occurrence of the problem. It may or may not yield further information if dereferenced.'
          type:
            - string
            - 'null'
        type:
          readOnly: true
          description: 'A URI reference that identifies the problem type'
          type: string
        description:
          readOnly: true
          type:
            - string
            - 'null'
    ForgotPassword:
      type: object
      description: ''
      deprecated: false
      properties:
        email:
          type:
            - string
            - 'null'
    ForgotPassword.jsonld:
      type: object
      description: ''
      deprecated: false
      properties:
        '@context':
          readOnly: true
          oneOf:
            -
              type: string
            -
              type: object
              properties:
                '@vocab':
                  type: string
                hydra:
                  type: string
                  enum: ['http://www.w3.org/ns/hydra/core#']
              required:
                - '@vocab'
                - hydra
              additionalProperties: true
        '@id':
          readOnly: true
          type: string
        '@type':
          readOnly: true
          type: string
        email:
          type:
            - string
            - 'null'
    Logout:
      type: object
      description: ''
      deprecated: false
    Logout.jsonld:
      type: object
      description: ''
      deprecated: false
      properties:
        '@context':
          readOnly: true
          oneOf:
            -
              type: string
            -
              type: object
              properties:
                '@vocab':
                  type: string
                hydra:
                  type: string
                  enum: ['http://www.w3.org/ns/hydra/core#']
              required:
                - '@vocab'
                - hydra
              additionalProperties: true
        '@id':
          readOnly: true
          type: string
        '@type':
          readOnly: true
          type: string
    Patient-patient.read:
      type: object
      description: ''
      deprecated: false
      properties:
        patientId:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        dateOfBirth:
          type: string
          format: date-time
        gender:
          type: string
        phone:
          type: string
        email:
          type:
            - string
            - 'null'
        address:
          type:
            - array
            - 'null'
          items:
            type: string
        district:
          type:
            - string
            - 'null'
        village:
          type:
            - string
            - 'null'
        postalCode:
          type:
            - string
            - 'null'
        emergencyContactName:
          type: string
        emergencyContactPhone:
          type: string
        bloodType:
          type:
            - string
            - 'null'
        height:
          type:
            - string
            - 'null'
        weight:
          type:
            - string
            - 'null'
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    Patient-patient.write:
      type: object
      description: ''
      deprecated: false
      properties:
        patientId:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        dateOfBirth:
          type: string
          format: date-time
        gender:
          type: string
        phone:
          type: string
        email:
          type:
            - string
            - 'null'
        address:
          type:
            - array
            - 'null'
          items:
            type: string
        district:
          type:
            - string
            - 'null'
        village:
          type:
            - string
            - 'null'
        postalCode:
          type:
            - string
            - 'null'
        emergencyContactName:
          type: string
        emergencyContactPhone:
          type: string
        bloodType:
          type:
            - string
            - 'null'
        height:
          type:
            - string
            - 'null'
        weight:
          type:
            - string
            - 'null'
    Patient.jsonld-patient.read:
      type: object
      description: ''
      deprecated: false
      properties:
        '@context':
          readOnly: true
          oneOf:
            -
              type: string
            -
              type: object
              properties:
                '@vocab':
                  type: string
                hydra:
                  type: string
                  enum: ['http://www.w3.org/ns/hydra/core#']
              required:
                - '@vocab'
                - hydra
              additionalProperties: true
        '@id':
          readOnly: true
          type: string
        '@type':
          readOnly: true
          type: string
        patientId:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        dateOfBirth:
          type: string
          format: date-time
        gender:
          type: string
        phone:
          type: string
        email:
          type:
            - string
            - 'null'
        address:
          type:
            - array
            - 'null'
          items:
            type: string
        district:
          type:
            - string
            - 'null'
        village:
          type:
            - string
            - 'null'
        postalCode:
          type:
            - string
            - 'null'
        emergencyContactName:
          type: string
        emergencyContactPhone:
          type: string
        bloodType:
          type:
            - string
            - 'null'
        height:
          type:
            - string
            - 'null'
        weight:
          type:
            - string
            - 'null'
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    Patient.jsonld-patient.write:
      type: object
      description: ''
      deprecated: false
      properties:
        patientId:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        dateOfBirth:
          type: string
          format: date-time
        gender:
          type: string
        phone:
          type: string
        email:
          type:
            - string
            - 'null'
        address:
          type:
            - array
            - 'null'
          items:
            type: string
        district:
          type:
            - string
            - 'null'
        village:
          type:
            - string
            - 'null'
        postalCode:
          type:
            - string
            - 'null'
        emergencyContactName:
          type: string
        emergencyContactPhone:
          type: string
        bloodType:
          type:
            - string
            - 'null'
        height:
          type:
            - string
            - 'null'
        weight:
          type:
            - string
            - 'null'
    Permission-permission.read:
      type: object
      description: ''
      deprecated: false
      properties:
        name:
          type: string
        displayName:
          type: string
        description:
          type:
            - string
            - 'null'
        resource:
          type: string
        action:
          type: string
        isSystemPermission:
          default: false
          example: false
          type: boolean
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    Permission-permission.write:
      type: object
      description: ''
      deprecated: false
      properties:
        name:
          type: string
        displayName:
          type: string
        description:
          type:
            - string
            - 'null'
        resource:
          type: string
        action:
          type: string
        isSystemPermission:
          default: false
          example: false
          type: boolean
    Permission.jsonld-permission.read:
      type: object
      description: ''
      deprecated: false
      properties:
        '@context':
          readOnly: true
          oneOf:
            -
              type: string
            -
              type: object
              properties:
                '@vocab':
                  type: string
                hydra:
                  type: string
                  enum: ['http://www.w3.org/ns/hydra/core#']
              required:
                - '@vocab'
                - hydra
              additionalProperties: true
        '@id':
          readOnly: true
          type: string
        '@type':
          readOnly: true
          type: string
        name:
          type: string
        displayName:
          type: string
        description:
          type:
            - string
            - 'null'
        resource:
          type: string
        action:
          type: string
        isSystemPermission:
          default: false
          example: false
          type: boolean
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    Permission.jsonld-permission.write:
      type: object
      description: ''
      deprecated: false
      properties:
        name:
          type: string
        displayName:
          type: string
        description:
          type:
            - string
            - 'null'
        resource:
          type: string
        action:
          type: string
        isSystemPermission:
          default: false
          example: false
          type: boolean
    Register:
      type: object
      description: ''
      deprecated: false
      properties:
        firstName:
          type:
            - string
            - 'null'
        lastName:
          type:
            - string
            - 'null'
        email:
          type:
            - string
            - 'null'
        password:
          type:
            - string
            - 'null'
        confirmPassword:
          type:
            - string
            - 'null'
    Register.jsonld:
      type: object
      description: ''
      deprecated: false
      properties:
        '@context':
          readOnly: true
          oneOf:
            -
              type: string
            -
              type: object
              properties:
                '@vocab':
                  type: string
                hydra:
                  type: string
                  enum: ['http://www.w3.org/ns/hydra/core#']
              required:
                - '@vocab'
                - hydra
              additionalProperties: true
        '@id':
          readOnly: true
          type: string
        '@type':
          readOnly: true
          type: string
        firstName:
          type:
            - string
            - 'null'
        lastName:
          type:
            - string
            - 'null'
        email:
          type:
            - string
            - 'null'
        password:
          type:
            - string
            - 'null'
        confirmPassword:
          type:
            - string
            - 'null'
    ResetPassword:
      type: object
      description: ''
      deprecated: false
      properties:
        token:
          type:
            - string
            - 'null'
        password:
          type:
            - string
            - 'null'
        confirmPassword:
          type:
            - string
            - 'null'
    ResetPassword.jsonld:
      type: object
      description: ''
      deprecated: false
      properties:
        '@context':
          readOnly: true
          oneOf:
            -
              type: string
            -
              type: object
              properties:
                '@vocab':
                  type: string
                hydra:
                  type: string
                  enum: ['http://www.w3.org/ns/hydra/core#']
              required:
                - '@vocab'
                - hydra
              additionalProperties: true
        '@id':
          readOnly: true
          type: string
        '@type':
          readOnly: true
          type: string
        token:
          type:
            - string
            - 'null'
        password:
          type:
            - string
            - 'null'
        confirmPassword:
          type:
            - string
            - 'null'
    Role-role.read:
      type: object
      description: ''
      deprecated: false
      properties:
        name:
          type: string
        displayName:
          type: string
        description:
          type:
            - string
            - 'null'
        parent:
          anyOf:
            -
              $ref: '#/components/schemas/Role-role.read'
            -
              type: 'null'
        isSystemRole:
          default: false
          example: false
          type: boolean
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    Role-role.write:
      type: object
      description: ''
      deprecated: false
      properties:
        name:
          type: string
        displayName:
          type: string
        description:
          type:
            - string
            - 'null'
        parent:
          anyOf:
            -
              $ref: '#/components/schemas/Role-role.write'
            -
              type: 'null'
        isSystemRole:
          default: false
          example: false
          type: boolean
    Role.jsonld-role.read:
      type: object
      description: ''
      deprecated: false
      properties:
        '@context':
          readOnly: true
          oneOf:
            -
              type: string
            -
              type: object
              properties:
                '@vocab':
                  type: string
                hydra:
                  type: string
                  enum: ['http://www.w3.org/ns/hydra/core#']
              required:
                - '@vocab'
                - hydra
              additionalProperties: true
        '@id':
          readOnly: true
          type: string
        '@type':
          readOnly: true
          type: string
        name:
          type: string
        displayName:
          type: string
        description:
          type:
            - string
            - 'null'
        parent:
          anyOf:
            -
              $ref: '#/components/schemas/Role.jsonld-role.read'
            -
              type: 'null'
        isSystemRole:
          default: false
          example: false
          type: boolean
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    Role.jsonld-role.write:
      type: object
      description: ''
      deprecated: false
      properties:
        name:
          type: string
        displayName:
          type: string
        description:
          type:
            - string
            - 'null'
        parent:
          anyOf:
            -
              $ref: '#/components/schemas/Role.jsonld-role.write'
            -
              type: 'null'
        isSystemRole:
          default: false
          example: false
          type: boolean
    RolePermission-role_permission.read:
      type: object
      description: ''
      deprecated: false
      properties:
        role:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        permission:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        grantedBy:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        grantedAt:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    RolePermission-role_permission.write:
      type: object
      description: ''
      deprecated: false
      properties:
        role:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        permission:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        grantedBy:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
    RolePermission.jsonld-role_permission.read:
      type: object
      description: ''
      deprecated: false
      properties:
        '@context':
          readOnly: true
          oneOf:
            -
              type: string
            -
              type: object
              properties:
                '@vocab':
                  type: string
                hydra:
                  type: string
                  enum: ['http://www.w3.org/ns/hydra/core#']
              required:
                - '@vocab'
                - hydra
              additionalProperties: true
        '@id':
          readOnly: true
          type: string
        '@type':
          readOnly: true
          type: string
        role:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        permission:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        grantedBy:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        grantedAt:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    RolePermission.jsonld-role_permission.write:
      type: object
      description: ''
      deprecated: false
      properties:
        role:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        permission:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        grantedBy:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
    SecurityEvent:
      type: object
      description: ''
      deprecated: false
      properties:
        id:
          readOnly: true
          type: string
        eventType:
          type: string
        category:
          type: string
        severity:
          default: medium
          example: medium
          type: string
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        ipAddress:
          type:
            - string
            - 'null'
        userAgent:
          type:
            - string
            - 'null'
        location:
          type:
            - array
            - 'null'
          items:
            type: string
        details:
          type:
            - array
            - 'null'
          items:
            type: string
        correlationId:
          type:
            - string
            - 'null'
        source:
          type:
            - string
            - 'null'
        isResolved:
          default: false
          example: false
          type: boolean
        resolvedAt:
          type:
            - string
            - 'null'
          format: date-time
        resolvedBy:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        timestamp:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        resolved:
          readOnly: true
          type: boolean
    SecurityEvent-security_event.read:
      type: object
      description: ''
      deprecated: false
      properties:
        eventType:
          type: string
        category:
          type: string
        severity:
          default: medium
          example: medium
          type: string
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        ipAddress:
          type:
            - string
            - 'null'
        userAgent:
          type:
            - string
            - 'null'
        location:
          type:
            - array
            - 'null'
          items:
            type: string
        details:
          type:
            - array
            - 'null'
          items:
            type: string
        correlationId:
          type:
            - string
            - 'null'
        source:
          type:
            - string
            - 'null'
        isResolved:
          default: false
          example: false
          type: boolean
        resolvedAt:
          type:
            - string
            - 'null'
          format: date-time
        resolvedBy:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        timestamp:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    SecurityEvent.jsonld:
      type: object
      description: ''
      deprecated: false
      properties:
        id:
          readOnly: true
          type: string
        eventType:
          type: string
        category:
          type: string
        severity:
          default: medium
          example: medium
          type: string
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        ipAddress:
          type:
            - string
            - 'null'
        userAgent:
          type:
            - string
            - 'null'
        location:
          type:
            - array
            - 'null'
          items:
            type: string
        details:
          type:
            - array
            - 'null'
          items:
            type: string
        correlationId:
          type:
            - string
            - 'null'
        source:
          type:
            - string
            - 'null'
        isResolved:
          default: false
          example: false
          type: boolean
        resolvedAt:
          type:
            - string
            - 'null'
          format: date-time
        resolvedBy:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        timestamp:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        resolved:
          readOnly: true
          type: boolean
    SecurityEvent.jsonld-security_event.read:
      type: object
      description: ''
      deprecated: false
      properties:
        '@context':
          readOnly: true
          oneOf:
            -
              type: string
            -
              type: object
              properties:
                '@vocab':
                  type: string
                hydra:
                  type: string
                  enum: ['http://www.w3.org/ns/hydra/core#']
              required:
                - '@vocab'
                - hydra
              additionalProperties: true
        '@id':
          readOnly: true
          type: string
        '@type':
          readOnly: true
          type: string
        eventType:
          type: string
        category:
          type: string
        severity:
          default: medium
          example: medium
          type: string
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        ipAddress:
          type:
            - string
            - 'null'
        userAgent:
          type:
            - string
            - 'null'
        location:
          type:
            - array
            - 'null'
          items:
            type: string
        details:
          type:
            - array
            - 'null'
          items:
            type: string
        correlationId:
          type:
            - string
            - 'null'
        source:
          type:
            - string
            - 'null'
        isResolved:
          default: false
          example: false
          type: boolean
        resolvedAt:
          type:
            - string
            - 'null'
          format: date-time
        resolvedBy:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        timestamp:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    SystemConfiguration-system_config.read:
      type: object
      description: ''
      deprecated: false
      properties:
        configKey:
          type: string
        configValue:
          type:
            - string
            - 'null'
        configType:
          default: string
          example: string
          type: string
        category:
          type: string
        description:
          type:
            - string
            - 'null'
        isSystemSetting:
          default: false
          example: false
          type: boolean
        isEditable:
          default: true
          example: true
          type: boolean
        validationRules:
          type:
            - array
            - 'null'
          items:
            type: string
        defaultValue:
          type:
            - string
            - 'null'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    SystemConfiguration-system_config.write:
      type: object
      description: ''
      deprecated: false
      properties:
        configKey:
          type: string
        configValue:
          type:
            - string
            - 'null'
        configType:
          default: string
          example: string
          type: string
        category:
          type: string
        description:
          type:
            - string
            - 'null'
        isSystemSetting:
          default: false
          example: false
          type: boolean
        isEditable:
          default: true
          example: true
          type: boolean
        validationRules:
          type:
            - array
            - 'null'
          items:
            type: string
        defaultValue:
          type:
            - string
            - 'null'
    SystemConfiguration.jsonld-system_config.read:
      type: object
      description: ''
      deprecated: false
      properties:
        '@context':
          readOnly: true
          oneOf:
            -
              type: string
            -
              type: object
              properties:
                '@vocab':
                  type: string
                hydra:
                  type: string
                  enum: ['http://www.w3.org/ns/hydra/core#']
              required:
                - '@vocab'
                - hydra
              additionalProperties: true
        '@id':
          readOnly: true
          type: string
        '@type':
          readOnly: true
          type: string
        configKey:
          type: string
        configValue:
          type:
            - string
            - 'null'
        configType:
          default: string
          example: string
          type: string
        category:
          type: string
        description:
          type:
            - string
            - 'null'
        isSystemSetting:
          default: false
          example: false
          type: boolean
        isEditable:
          default: true
          example: true
          type: boolean
        validationRules:
          type:
            - array
            - 'null'
          items:
            type: string
        defaultValue:
          type:
            - string
            - 'null'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    SystemConfiguration.jsonld-system_config.write:
      type: object
      description: ''
      deprecated: false
      properties:
        configKey:
          type: string
        configValue:
          type:
            - string
            - 'null'
        configType:
          default: string
          example: string
          type: string
        category:
          type: string
        description:
          type:
            - string
            - 'null'
        isSystemSetting:
          default: false
          example: false
          type: boolean
        isEditable:
          default: true
          example: true
          type: boolean
        validationRules:
          type:
            - array
            - 'null'
          items:
            type: string
        defaultValue:
          type:
            - string
            - 'null'
    User-user.read:
      type: object
      description: ''
      deprecated: false
      properties:
        email:
          type: string
        phone:
          type:
            - string
            - 'null'
        roles:
          type: array
          items:
            type: string
        status:
          default: pending
          example: pending
          type: string
        lastLoginAt:
          type:
            - string
            - 'null'
          format: date-time
        profile:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        doctor:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        patient:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    User-user.write:
      type: object
      description: ''
      deprecated: false
      properties:
        email:
          type: string
        phone:
          type:
            - string
            - 'null'
        password:
          type: string
    User.jsonld-user.read:
      type: object
      description: ''
      deprecated: false
      properties:
        '@context':
          readOnly: true
          oneOf:
            -
              type: string
            -
              type: object
              properties:
                '@vocab':
                  type: string
                hydra:
                  type: string
                  enum: ['http://www.w3.org/ns/hydra/core#']
              required:
                - '@vocab'
                - hydra
              additionalProperties: true
        '@id':
          readOnly: true
          type: string
        '@type':
          readOnly: true
          type: string
        email:
          type: string
        phone:
          type:
            - string
            - 'null'
        roles:
          type: array
          items:
            type: string
        status:
          default: pending
          example: pending
          type: string
        lastLoginAt:
          type:
            - string
            - 'null'
          format: date-time
        profile:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        doctor:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        patient:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    User.jsonld-user.write:
      type: object
      description: ''
      deprecated: false
      properties:
        email:
          type: string
        phone:
          type:
            - string
            - 'null'
        password:
          type: string
    User2FASetting-user_2fa.read:
      type: object
      description: ''
      deprecated: false
      properties:
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        secretKey:
          type: string
        backupCodes:
          type:
            - array
            - 'null'
          items:
            type: string
        enabledAt:
          type:
            - string
            - 'null'
          format: date-time
        lastUsedAt:
          type:
            - string
            - 'null'
          format: date-time
        method:
          default: totp
          example: totp
          type: string
        phoneNumber:
          type:
            - string
            - 'null'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    User2FASetting-user_2fa.write:
      type: object
      description: ''
      deprecated: false
      properties:
        secretKey:
          type: string
        backupCodes:
          type:
            - array
            - 'null'
          items:
            type: string
        method:
          default: totp
          example: totp
          type: string
        phoneNumber:
          type:
            - string
            - 'null'
    User2FASetting.jsonld-user_2fa.read:
      type: object
      description: ''
      deprecated: false
      properties:
        '@context':
          readOnly: true
          oneOf:
            -
              type: string
            -
              type: object
              properties:
                '@vocab':
                  type: string
                hydra:
                  type: string
                  enum: ['http://www.w3.org/ns/hydra/core#']
              required:
                - '@vocab'
                - hydra
              additionalProperties: true
        '@id':
          readOnly: true
          type: string
        '@type':
          readOnly: true
          type: string
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        secretKey:
          type: string
        backupCodes:
          type:
            - array
            - 'null'
          items:
            type: string
        enabledAt:
          type:
            - string
            - 'null'
          format: date-time
        lastUsedAt:
          type:
            - string
            - 'null'
          format: date-time
        method:
          default: totp
          example: totp
          type: string
        phoneNumber:
          type:
            - string
            - 'null'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    User2FASetting.jsonld-user_2fa.write:
      type: object
      description: ''
      deprecated: false
      properties:
        secretKey:
          type: string
        backupCodes:
          type:
            - array
            - 'null'
          items:
            type: string
        method:
          default: totp
          example: totp
          type: string
        phoneNumber:
          type:
            - string
            - 'null'
    UserNotification-notification.read:
      type: object
      description: ''
      deprecated: false
      properties:
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        title:
          type: string
        message:
          type: string
        type:
          default: info
          example: info
          type: string
        priority:
          default: medium
          example: medium
          type: string
        channel:
          default: in_app
          example: in_app
          type: string
        isRead:
          default: false
          example: false
          type: boolean
        readAt:
          type:
            - string
            - 'null'
          format: date-time
        sentAt:
          type:
            - string
            - 'null'
          format: date-time
        expiresAt:
          type:
            - string
            - 'null'
          format: date-time
        actionUrl:
          type:
            - string
            - 'null'
        actionText:
          type:
            - string
            - 'null'
    UserNotification-notification.write:
      type: object
      description: ''
      deprecated: false
      properties:
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        title:
          type: string
        message:
          type: string
        type:
          default: info
          example: info
          type: string
        priority:
          default: medium
          example: medium
          type: string
        channel:
          default: in_app
          example: in_app
          type: string
        isRead:
          default: false
          example: false
          type: boolean
        actionUrl:
          type:
            - string
            - 'null'
        actionText:
          type:
            - string
            - 'null'
    UserNotification.jsonld-notification.read:
      type: object
      description: ''
      deprecated: false
      properties:
        '@context':
          readOnly: true
          oneOf:
            -
              type: string
            -
              type: object
              properties:
                '@vocab':
                  type: string
                hydra:
                  type: string
                  enum: ['http://www.w3.org/ns/hydra/core#']
              required:
                - '@vocab'
                - hydra
              additionalProperties: true
        '@id':
          readOnly: true
          type: string
        '@type':
          readOnly: true
          type: string
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        title:
          type: string
        message:
          type: string
        type:
          default: info
          example: info
          type: string
        priority:
          default: medium
          example: medium
          type: string
        channel:
          default: in_app
          example: in_app
          type: string
        isRead:
          default: false
          example: false
          type: boolean
        readAt:
          type:
            - string
            - 'null'
          format: date-time
        sentAt:
          type:
            - string
            - 'null'
          format: date-time
        expiresAt:
          type:
            - string
            - 'null'
          format: date-time
        actionUrl:
          type:
            - string
            - 'null'
        actionText:
          type:
            - string
            - 'null'
    UserNotification.jsonld-notification.write:
      type: object
      description: ''
      deprecated: false
      properties:
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        title:
          type: string
        message:
          type: string
        type:
          default: info
          example: info
          type: string
        priority:
          default: medium
          example: medium
          type: string
        channel:
          default: in_app
          example: in_app
          type: string
        isRead:
          default: false
          example: false
          type: boolean
        actionUrl:
          type:
            - string
            - 'null'
        actionText:
          type:
            - string
            - 'null'
    UserProfile-user_profile.read:
      type: object
      description: ''
      deprecated: false
      properties:
        firstName:
          type: string
        lastName:
          type: string
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    UserProfile-user_profile.write:
      type: object
      description: ''
      deprecated: false
      properties:
        firstName:
          type: string
        lastName:
          type: string
    UserProfile.jsonld-user_profile.read:
      type: object
      description: ''
      deprecated: false
      properties:
        '@context':
          readOnly: true
          oneOf:
            -
              type: string
            -
              type: object
              properties:
                '@vocab':
                  type: string
                hydra:
                  type: string
                  enum: ['http://www.w3.org/ns/hydra/core#']
              required:
                - '@vocab'
                - hydra
              additionalProperties: true
        '@id':
          readOnly: true
          type: string
        '@type':
          readOnly: true
          type: string
        firstName:
          type: string
        lastName:
          type: string
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    UserProfile.jsonld-user_profile.write:
      type: object
      description: ''
      deprecated: false
      properties:
        firstName:
          type: string
        lastName:
          type: string
    UserRole-user_role.read:
      type: object
      description: ''
      deprecated: false
      properties:
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        role:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        assignedBy:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        assignedAt:
          type: string
          format: date-time
        notes:
          type:
            - string
            - 'null'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    UserRole-user_role.write:
      type: object
      description: ''
      deprecated: false
      properties:
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        role:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        assignedBy:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        notes:
          type:
            - string
            - 'null'
    UserRole.jsonld-user_role.read:
      type: object
      description: ''
      deprecated: false
      properties:
        '@context':
          readOnly: true
          oneOf:
            -
              type: string
            -
              type: object
              properties:
                '@vocab':
                  type: string
                hydra:
                  type: string
                  enum: ['http://www.w3.org/ns/hydra/core#']
              required:
                - '@vocab'
                - hydra
              additionalProperties: true
        '@id':
          readOnly: true
          type: string
        '@type':
          readOnly: true
          type: string
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        role:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        assignedBy:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        assignedAt:
          type: string
          format: date-time
        notes:
          type:
            - string
            - 'null'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    UserRole.jsonld-user_role.write:
      type: object
      description: ''
      deprecated: false
      properties:
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        role:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        assignedBy:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        notes:
          type:
            - string
            - 'null'
    UserVerification-user_verification.read:
      type: object
      description: ''
      deprecated: false
      properties:
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        verificationType:
          type: string
        verificationMethod:
          type:
            - string
            - 'null'
        token:
          type: string
        expiresAt:
          type: string
          format: date-time
        verifiedAt:
          type:
            - string
            - 'null'
          format: date-time
        ipAddress:
          type:
            - string
            - 'null'
        userAgent:
          type:
            - string
            - 'null'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    UserVerification-user_verification.write:
      type: object
      description: ''
      deprecated: false
      properties:
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        verificationType:
          type: string
        verificationMethod:
          type:
            - string
            - 'null'
        token:
          type: string
    UserVerification.jsonld-user_verification.read:
      type: object
      description: ''
      deprecated: false
      properties:
        '@context':
          readOnly: true
          oneOf:
            -
              type: string
            -
              type: object
              properties:
                '@vocab':
                  type: string
                hydra:
                  type: string
                  enum: ['http://www.w3.org/ns/hydra/core#']
              required:
                - '@vocab'
                - hydra
              additionalProperties: true
        '@id':
          readOnly: true
          type: string
        '@type':
          readOnly: true
          type: string
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        verificationType:
          type: string
        verificationMethod:
          type:
            - string
            - 'null'
        token:
          type: string
        expiresAt:
          type: string
          format: date-time
        verifiedAt:
          type:
            - string
            - 'null'
          format: date-time
        ipAddress:
          type:
            - string
            - 'null'
        userAgent:
          type:
            - string
            - 'null'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    UserVerification.jsonld-user_verification.write:
      type: object
      description: ''
      deprecated: false
      properties:
        user:
          type:
            - string
            - 'null'
          format: iri-reference
          example: 'https://example.com/'
        verificationType:
          type: string
        verificationMethod:
          type:
            - string
            - 'null'
        token:
          type: string
  responses: {  }
  parameters: {  }
  examples: {  }
  requestBodies: {  }
  headers: {  }
  securitySchemes:
    JWT:
      type: http
      scheme: bearer
      bearerFormat: JWT
security:
  -
    JWT: []
tags:
  -
    name: ForgotPassword
  -
    name: Logout
  -
    name: Register
  -
    name: ResetPassword
  -
    name: AuditLog
  -
    name: ConfigurationHistory
  -
    name: Doctor
  -
    name: Patient
  -
    name: Permission
  -
    name: Role
  -
    name: RolePermission
  -
    name: SecurityEvent
  -
    name: SystemConfiguration
  -
    name: User
  -
    name: User2FASetting
  -
    name: UserNotification
  -
    name: UserProfile
  -
    name: UserRole
  -
    name: UserVerification
webhooks: {  }

