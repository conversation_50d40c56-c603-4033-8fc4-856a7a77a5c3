<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diasys - Reset Password</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-center">Reset Password</h3>
                    </div>
                    <div class="card-body">
                        {% if error %}
                            <div class="alert alert-danger">
                                <strong>Error:</strong> {{ error }}
                            </div>
                        {% endif %}

                        {% if not error %}
                        <form method="post" action="{{ path('app_reset_password', {'token': token}) }}">
                            <input type="hidden" name="_csrf_token" value="{{ csrf_token('reset_password') }}">
                            <input type="hidden" name="token" value="{{ token }}">

                            <div class="form-group">
                                <label for="password">New Password</label>
                                <input type="password" id="password" name="password" class="form-control" required autofocus autocomplete="new-password" minlength="8">
                                <small class="form-text text-muted">Password must be at least 8 characters long.</small>
                            </div>

                            <div class="form-group">
                                <label for="confirmPassword">Confirm New Password</label>
                                <input type="password" id="confirmPassword" name="confirmPassword" class="form-control" required autocomplete="new-password">
                            </div>

                            <button type="submit" class="btn btn-primary btn-block">Reset Password</button>
                        </form>
                        {% endif %}

                        <div class="text-center mt-3">
                            <p>Remember your password? <a href="{{ path('app_login') }}">Sign in</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>