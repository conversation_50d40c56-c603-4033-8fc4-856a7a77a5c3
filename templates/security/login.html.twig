<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DiaSys Login</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-center">DiaSys Login</h3>
                    </div>
                    <div class="card-body">
                        {% for message in app.flashes('success') %}
                            <div class="alert alert-success">
                                {{ message }}
                            </div>
                        {% endfor %}

                        {% for message in app.flashes('error') %}
                            <div class="alert alert-danger">
                                {{ message }}
                            </div>
                        {% endfor %}

                        <form method="post" action="{{ path('app_login_check') }}">
                            {% if error %}
                                <div class="alert alert-danger">
                                    {{ error.messageKey|trans(error.messageData, 'security') }}
                                </div>
                            {% endif %}

                            {% if app.user %}
                                <div class="alert alert-info">
                                    You are logged in as {{ app.user.userIdentifier }}, <a href="{{ path('app_logout') }}">Logout</a>
                                </div>
                            {% endif %}

                            <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}">
                            

                            <div class="form-group">
                                <label for="username">Email</label>
                                <input type="email" id="username" name="_username" class="form-control" value="{{ last_username|default('') }}" required autofocus autocomplete="email">
                            </div>

                            <div class="form-group">
                                <label for="password">Password</label>
                                <input type="password" id="password" name="_password" class="form-control" required autocomplete="current-password">
                            </div>

                            <button type="submit" class="btn btn-primary btn-block">Sign in</button>
                        </form>

                        <div class="text-center mt-3">
                            <p><a href="{{ path('app_forgot_password') }}">Forgot your password?</a></p>
                            <p>Don't have an account? <a href="{{ path('app_register') }}">Register</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>