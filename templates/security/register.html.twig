<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DiaSys Admin Registration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-center">DiaSys Admin Registration</h3>
                    </div>
                    <div class="card-body">
                        <form method="post" action="{{ path('app_register') }}">
                            {% for message in app.flashes('success') %}
                                <div class="alert alert-success">
                                    {{ message }}
                                </div>
                            {% endfor %}

                            {% if error %}
                                <div class="alert alert-danger">
                                    {{ error.messageKey }}
                                </div>
                            {% endif %}

                            {% if app.user %}
                                <div class="alert alert-info">
                                    You are logged in as {{ app.user.userIdentifier }}, <a href="{{ path('app_logout') }}">Logout</a>
                                </div>
                            {% endif %}

                            <input type="hidden" name="_csrf_token" value="{{ csrf_token('register') }}">

                            <div class="form-group">
                                <label for="firstName">First Name</label>
                                <input type="text" id="firstName" name="firstName" class="form-control" value="{{ last_username|default('') }}" required autofocus autocomplete="given-name">
                            </div>

                            <div class="form-group">
                                <label for="lastName">Last Name</label>
                                <input type="text" id="lastName" name="lastName" class="form-control" required autocomplete="family-name">
                            </div>

                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" id="email" name="email" class="form-control" required autocomplete="email">
                            </div>

                            <div class="form-group">
                                <label for="password">Password</label>
                                <input type="password" id="password" name="password" class="form-control" required autocomplete="new-password">
                            </div>

                            <div class="form-group">
                                <label for="confirmPassword">Confirm Password</label>
                                <input type="password" id="confirmPassword" name="confirmPassword" class="form-control" required autocomplete="new-password">
                            </div>

                            <button type="submit" class="btn btn-primary btn-block">Create Account</button>
                        </form>

                        <div class="text-center mt-3">
                            <p>Already have an account? <a href="{{ path('app_login') }}">Sign in</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>