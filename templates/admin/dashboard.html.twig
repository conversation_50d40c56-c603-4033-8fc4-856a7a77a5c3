{% extends '@EasyAdmin/page/content.html.twig' %}

{% block content_title %}Healthcare System Dashboard{% endblock %}

{% block page_actions %}
    <div class="d-flex align-items-center">
        <span class="badge bg-success me-2">Live</span>
        <span class="text-muted small">Updated: {{ "now"|date("M j, Y g:i A") }}</span>
    </div>
{% endblock %}

{% block main %}
<div class="container-fluid">
    {% if metrics is defined %}
        <!-- Key Metrics Row -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="card-title text-uppercase text-muted mb-0">Total Users</h5>
                                <span class="h2 font-weight-bold mb-0 text-body">{{ metrics.total_users|default(0) }}</span>
                            </div>
                            <div class="col-auto">
                                <div class="bg-primary bg-opacity-10 text-primary rounded-circle p-3 shadow-sm">
                                    <i class="fas fa-users fa-lg"></i>
                                </div>
                            </div>
                        </div>
                        <p class="mt-3 mb-0 text-sm">
                            <span class="text-success me-2">
                                <i class="fas fa-arrow-up"></i> 12.5%
                            </span>
                            <span class="text-nowrap">Since last month</span>
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="card-title text-uppercase text-muted mb-0">Doctors</h5>
                                <span class="h2 font-weight-bold mb-0 text-body">{{ metrics.total_doctors|default(0) }}</span>
                            </div>
                            <div class="col-auto">
                                <div class="bg-success bg-opacity-10 text-success rounded-circle p-3 shadow-sm">
                                    <i class="fas fa-user-md fa-lg"></i>
                                </div>
                            </div>
                        </div>
                        <p class="mt-3 mb-0 text-sm">
                            <span class="text-success me-2">
                                <i class="fas fa-arrow-up"></i> 8.2%
                            </span>
                            <span class="text-nowrap">Since last month</span>
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="card-title text-uppercase text-muted mb-0">Patients</h5>
                                <span class="h2 font-weight-bold mb-0 text-body">{{ metrics.total_patients|default(0) }}</span>
                            </div>
                            <div class="col-auto">
                                <div class="bg-info bg-opacity-10 text-info rounded-circle p-3 shadow-sm">
                                    <i class="fas fa-hospital-user fa-lg"></i>
                                </div>
                            </div>
                        </div>
                        <p class="mt-3 mb-0 text-sm">
                            <span class="text-success me-2">
                                <i class="fas fa-arrow-up"></i> 15.3%
                            </span>
                            <span class="text-nowrap">Since last month</span>
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="card-title text-uppercase text-muted mb-0">Revenue</h5>
                                <span class="h2 font-weight-bold mb-0 text-body">${{ metrics.total_revenue|default(0)|number_format(0) }}</span>
                            </div>
                            <div class="col-auto">
                                <div class="bg-warning bg-opacity-10 text-warning rounded-circle p-3 shadow-sm">
                                    <i class="fas fa-dollar-sign fa-lg"></i>
                                </div>
                            </div>
                        </div>
                        <p class="mt-3 mb-0 text-sm">
                            <span class="text-success me-2">
                                <i class="fas fa-arrow-up"></i> 22.1%
                            </span>
                            <span class="text-nowrap">Since last month</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activity and Health Row -->
        <div class="row">
            <div class="col-lg-9 mb-4">
                <h5 class="text-body mb-3">
                    <i class="fas fa-chart-line me-2 text-primary"></i>Today's Activity
                </h5>
                <div class="row g-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body text-center">
                                <h5 class="card-title text-uppercase text-muted mb-2">Today's Appointments</h5>
                                <span class="h2 font-weight-bold mb-0 text-body">{{ metrics.today_appointments|default(0) }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body text-center">
                                <h5 class="card-title text-uppercase text-muted mb-2">Pending</h5>
                                <span class="h2 font-weight-bold mb-0 text-body">{{ metrics.pending_appointments|default(0) }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body text-center">
                                <h5 class="card-title text-uppercase text-muted mb-2">Tests</h5>
                                <span class="h2 font-weight-bold mb-0 text-body">{{ metrics.total_tests|default(0) }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body text-center">
                                <h5 class="card-title text-uppercase text-muted mb-0">Critical</h5>
                                <span class="h2 font-weight-bold mb-0 text-body">{{ metrics.critical_results|default(0) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 mb-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-transparent border-0">
                        <h6 class="card-title mb-0 text-body">
                            <i class="fas fa-heartbeat me-2 text-success"></i>System Health
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if metrics.system_health is defined %}
                            <dl class="row mb-0">
                                <dt class="col-sm-5 text-body fw-semibold">
                                    <i class="fas fa-database me-2 {% if metrics.system_health.database.status == 'healthy' %}text-success{% else %}text-danger{% endif %}"></i>Database
                                </dt>
                                <dd class="col-sm-7 {% if metrics.system_health.database.status == 'healthy' %}text-success{% else %}text-danger{% endif %}">
                                    {{ metrics.system_health.database.message }}
                                </dd>

                                <dt class="col-sm-5 text-body fw-semibold">
                                    <i class="fas fa-hdd me-2 {% if metrics.system_health.disk_space.status == 'healthy' %}text-success{% else %}text-warning{% endif %}"></i>Disk Space
                                </dt>
                                <dd class="col-sm-7 {% if metrics.system_health.disk_space.status == 'healthy' %}text-success{% else %}text-warning{% endif %}">
                                    {{ metrics.system_health.disk_space.message }}
                                </dd>

                                <dt class="col-sm-5 text-body fw-semibold">
                                    <i class="fas fa-memory me-2 {% if metrics.system_health.memory.status == 'healthy' %}text-success{% else %}text-warning{% endif %}"></i>Memory
                                </dt>
                                <dd class="col-sm-7 {% if metrics.system_health.memory.status == 'healthy' %}text-success{% else %}text-warning{% endif %}">
                                    {{ metrics.system_health.memory.message }}
                                </dd>

                                <dt class="col-sm-5 text-body fw-semibold">
                                    <i class="fas fa-tachometer-alt me-2 {% if metrics.system_health.cache.status == 'healthy' %}text-success{% else %}text-warning{% endif %}"></i>Cache
                                </dt>
                                <dd class="col-sm-7 {% if metrics.system_health.cache.status == 'healthy' %}text-success{% else %}text-warning{% endif %}">
                                    {{ metrics.system_health.cache.message }}
                                </dd>
                            </dl>
                        {% else %}
                            <div class="text-center py-3">
                                <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                                <p class="text-muted mb-0 small">No health data available</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    {% else %}
        <div class="alert alert-warning border-0">
            <h5 class="alert-heading">No Metrics Data Available</h5>
            <p class="mb-0">Unable to load dashboard metrics. Please check system configuration.</p>
        </div>
    {% endif %}
</div>
{% endblock %}