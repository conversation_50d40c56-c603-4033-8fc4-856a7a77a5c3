{% extends 'emails/base.html.twig' %}

{% block subject %}{{ notification.title }}{% endblock %}

{% block email_content %}
    <h2>{{ notification.title }}</h2>

    <p>Dear {{ recipient.name }},</p>

    <div style="background-color: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0;">
        {{ notification.message|raw }}
    </div>

    {% if notification.actionUrl %}
    <div style="text-align: center; margin: 30px 0;">
        <a href="{{ notification.actionUrl }}" class="button">{{ notification.actionText ?: 'Take Action' }}</a>
    </div>
    {% endif %}

    {% if notification.details %}
    <h3>Additional Details:</h3>
    <ul>
        {% for key, value in notification.details %}
        <li><strong>{{ key|title }}:</strong> {{ value }}</li>
        {% endfor %}
    </ul>
    {% endif %}

    <p>This notification was sent on {{ 'now'|date('F j, Y \a\t g:i A') }}.</p>

    {% if notification.priority == 'urgent' %}
    <p style="color: #dc3545;"><strong>This is an urgent notification requiring immediate attention.</strong></p>
    {% endif %}

    <p>Best regards,<br>The DiaSys Team</p>
{% endblock %}