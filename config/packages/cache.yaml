# Cache Configuration for Doctrine and Application Performance
framework:
    cache:
        # Default cache configuration
        app: cache.adapter.filesystem
        system: cache.adapter.filesystem

        pools:
            # Doctrine specific cache pools
            doctrine.result_cache_pool:
                adapter: cache.adapter.filesystem
                public: false
                tags: true

            doctrine.system_cache_pool:
                adapter: cache.adapter.filesystem
                public: false

            doctrine.query_cache_pool:
                adapter: cache.adapter.filesystem
                public: false

            doctrine.metadata_cache_pool:
                adapter: cache.adapter.filesystem
                public: false

            # Application specific cache pools
            app.cache_pool:
                adapter: cache.adapter.filesystem
                public: false
                default_lifetime: 3600

            # User session cache
            app.session_cache:
                adapter: cache.adapter.filesystem
                public: false
                default_lifetime: 3600

            # Rate limiting cache pool with Redis backend
            app.rate_limiter_cache:
                adapter: cache.adapter.redis
                public: false
                default_lifetime: 3600

# Environment-specific cache configurations
when@dev:
    framework:
        cache:
            pools:
                doctrine.result_cache_pool:
                    adapter: cache.adapter.array
                doctrine.system_cache_pool:
                    adapter: cache.adapter.array
                doctrine.query_cache_pool:
                    adapter: cache.adapter.array
                doctrine.metadata_cache_pool:
                    adapter: cache.adapter.array

when@test:
    framework:
        cache:
            pools:
                doctrine.result_cache_pool:
                    adapter: cache.adapter.array
                doctrine.system_cache_pool:
                    adapter: cache.adapter.array
                doctrine.query_cache_pool:
                    adapter: cache.adapter.array
                doctrine.metadata_cache_pool:
                    adapter: cache.adapter.array
                app.cache_pool:
                    adapter: cache.adapter.array
                app.session_cache:
                    adapter: cache.adapter.array

when@prod:
    framework:
        cache:
            pools:
                doctrine.result_cache_pool:
                    adapter: cache.adapter.filesystem
                    default_lifetime: 3600
                doctrine.system_cache_pool:
                    adapter: cache.adapter.filesystem
                    default_lifetime: 3600
                doctrine.query_cache_pool:
                    adapter: cache.adapter.filesystem
                    default_lifetime: 1800
                doctrine.metadata_cache_pool:
                    adapter: cache.adapter.filesystem
                    default_lifetime: 3600