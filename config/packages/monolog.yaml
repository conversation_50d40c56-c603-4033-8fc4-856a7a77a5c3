# Monolog Configuration for Doctrine Query Logging
monolog:
    channels: ['doctrine']
    handlers:
        # Main application log
        main:
            type: stream
            path: '%kernel.logs_dir%/%kernel.environment%.log'
            level: debug
            channels: ['!doctrine']

        # Doctrine query logging
        doctrine_queries:
            type: stream
            path: '%kernel.logs_dir%/doctrine_queries.log'
            level: debug
            channels: [doctrine]
            formatter: doctrine_query_formatter

        # Slow query logging
        doctrine_slow_queries:
            type: stream
            path: '%kernel.logs_dir%/doctrine_slow_queries.log'
            level: info
            channels: [doctrine]
            formatter: doctrine_slow_query_formatter

        # Console output for development
        console:
            type: console
            process_psr_3_messages: false
            channels: ['!doctrine', '!event']

# Custom formatters for Doctrine logging
services:
    doctrine_query_formatter:
        class: Monolog\Formatter\LineFormatter
        arguments:
            - "[%%datetime%%] %%channel%%.%%level_name%%: %%message%% %%context%% %%extra%%\n"
            - 'Y-m-d H:i:s'
            - true
            - true

    doctrine_slow_query_formatter:
        class: Monolog\Formatter\LineFormatter
        arguments:
            - "[%%datetime%%] SLOW QUERY %%level_name%%: %%message%% (%%extra.execution_time%%ms)\n"
            - 'Y-m-d H:i:s'
            - true
            - true

# Environment-specific logging configurations
when@dev:
    monolog:
        handlers:
            doctrine_queries:
                level: debug
            doctrine_slow_queries:
                level: info

when@prod:
    monolog:
        handlers:
            doctrine_queries:
                level: warning
            doctrine_slow_queries:
                level: error

when@test:
    monolog:
        handlers:
            doctrine_queries:
                level: emergency
            doctrine_slow_queries:
                level: emergency