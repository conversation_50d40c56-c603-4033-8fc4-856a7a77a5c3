# Rate Limiting Configuration for API Platform
framework:
    rate_limiter:
        # Anonymous API rate limiter (IP-based)
        anonymous_api:
            policy: 'fixed_window'
            limit: 100
            interval: '1 hour'
            cache_pool: 'app.rate_limiter_cache'

        # Authenticated API rate limiter (User ID-based)
        authenticated_api:
            policy: 'fixed_window'
            limit: 1000
            interval: '1 hour'
            cache_pool: 'app.rate_limiter_cache'

        # Admin API rate limiter (higher limits)
        admin_api:
            policy: 'fixed_window'
            limit: 5000
            interval: '1 hour'
            cache_pool: 'app.rate_limiter_cache'

        # Login rate limiter for authentication endpoints
        login_attempts:
            policy: 'fixed_window'
            limit: 5
            interval: '15 minutes'
            cache_pool: 'app.rate_limiter_cache'