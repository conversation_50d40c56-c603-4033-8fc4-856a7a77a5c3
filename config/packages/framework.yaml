# see https://symfony.com/doc/current/reference/configuration/framework.html
framework:
    secret: '%env(APP_SECRET)%'

    # Note that the session will be started ONLY if you read or write from it.
    session: true

    # Enable attributes for Symfony 7.x compatibility
    serializer: { enable_attributes: true }

    # Redis configuration for cache and rate limiting
    cache:
        default_redis_provider: 'redis://127.0.0.1:32775'

    #esi: true
    #fragments: true

when@test:
    framework:
        test: true
        session:
            storage_factory_id: session.storage.factory.mock_file
