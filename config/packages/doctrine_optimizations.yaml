# Doctrine Performance Optimizations Configuration
# This file contains advanced Doctrine optimizations for production use

doctrine:
    dbal:
        # Connection pooling and performance optimizations
        options:
            # Connection pooling settings
            PDO::ATTR_PERSISTENT: true
            PDO::MYSQL_ATTR_USE_BUFFERED_QUERY: true
        charset: utf8mb4

        # Connection pool configuration
        pooled: true

# Framework cache pools for Doctrine
framework:
    cache:
        pools:
            # Doctrine connection pool
            doctrine.connection_pool:
                adapter: cache.adapter.filesystem
                public: false

            # Doctrine result cache pool
            doctrine.result_cache_pool:
                adapter: cache.adapter.filesystem
                public: false

            # Doctrine system cache pool
            doctrine.system_cache_pool:
                adapter: cache.adapter.filesystem
                public: false

            # Doctrine query cache pool
            doctrine.query_cache_pool:
                adapter: cache.adapter.filesystem
                public: false

            # Doctrine metadata cache pool
            doctrine.metadata_cache_pool:
                adapter: cache.adapter.filesystem
                public: false

# Monolog configuration for Doctrine query logging
monolog:
    handlers:
        doctrine_queries:
            type: stream
            path: '%kernel.logs_dir%/doctrine_queries.log'
            level: debug
            channels: [doctrine]
        doctrine_slow_queries:
            type: stream
            path: '%kernel.logs_dir%/doctrine_slow_queries.log'
            level: debug
            channels: [doctrine]
            formatter: monolog.formatter.line_formatter

# Services for Doctrine optimization
services:
    # Doctrine SQL Logger for development
    doctrine.logger.file:
        class: Doctrine\DBAL\Logging\FileSQLLogger
        arguments:
            - '%kernel.logs_dir%/doctrine_queries.log'

    doctrine.logger.console:
        class: Doctrine\DBAL\Logging\DebugStack
        public: true

# Environment-specific configurations
when@dev:
    doctrine:
        dbal:
            logging: true
            profiling: true

when@prod:
    doctrine:
        dbal:
            logging: false
            profiling: false

when@test:
    doctrine:
        dbal:
            logging: false
            profiling: false