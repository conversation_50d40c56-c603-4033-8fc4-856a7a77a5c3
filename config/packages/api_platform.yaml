api_platform:
    # Global API settings
    title: 'DiaSys Healthcare API'
    description: 'REST API for DiaSys Healthcare Management System'
    version: '3.0.0-beta'
    show_webby: false

    # API Versioning:
    # API Platform supports versioning through:
    # 1. Accept header: Accept: application/json; version=1.0
    # 2. Custom header: Api-Version: 1.0
    # 3. URL path: /api/v1/resource (configured per operation)
    # Versioned formats are configured in the formats section below

    # Resource mapping paths
    mapping:
        paths:
            - '%kernel.project_dir%/src/Entity'

    # Global defaults for all resources
    defaults:
        # Pagination settings
        pagination_enabled: true
        pagination_client_enabled: true
        pagination_client_items_per_page: true
        pagination_client_partial: true
        pagination_items_per_page: 30
        pagination_maximum_items_per_page: 100
        pagination_partial: false
        pagination_via_cursor: []

        # Cache headers
        cache_headers:
            etag: true
            max_age: 3600
            shared_max_age: 3600
            vary: ['Content-Type', 'Authorization', 'Origin', 'Accept', 'Api-Version']

        # Serialization settings
        normalization_context:
            skip_null_values: true
        denormalization_context: []

        # Security and stateless
        stateless: true

        # URL generation strategy
        url_generation_strategy: !php/const ApiPlatform\Metadata\UrlGeneratorInterface::ABS_PATH

        # Other defaults
        collectDenormalizationErrors: false

        # Enable RFC 7807 compliant errors for better error formatting
        rfc_7807_compliant_errors: true

    # Supported formats with versioning support
    formats:
        jsonld:
            mime_types: ['application/ld+json', 'application/ld+json; version=1.0']
        json:
            mime_types: ['application/json', 'application/json; version=1.0']
        xml:
            mime_types: ['application/xml', 'text/xml', 'application/xml; version=1.0', 'text/xml; version=1.0']
        html:
            mime_types: ['text/html', 'text/html; version=1.0']

    # PATCH formats
    patch_formats:
        json: ['application/merge-patch+json']

    # Error formats
    error_formats:
        jsonproblem:
            mime_types: ['application/problem+json']
        jsonld:
            mime_types: ['application/ld+json']

    # Collection settings
    collection:
        exists_parameter_name: 'exists'
        order: 'ASC'
        order_parameter_name: 'order'
        pagination:
            page_parameter_name: 'page'
            enabled_parameter_name: 'pagination'
            items_per_page_parameter_name: 'itemsPerPage'
            partial_parameter_name: 'partial'

    # Eager loading configuration
    eager_loading:
        enabled: true
        fetch_partial: false
        max_joins: 30
        force_eager: false

    # Validator configuration
    validator:
        serialize_payload_fields: []
        query_parameter_validation: true

    # Documentation settings
    enable_swagger: true
    enable_swagger_ui: true
    enable_re_doc: true
    enable_entrypoint: true
    enable_docs: true
    enable_profiler: true

    # Swagger/OpenAPI configuration
    swagger:
        versions: [3]
        api_keys:
            JWT:
                name: Authorization
                type: header
        swagger_ui_extra_configuration:
            docExpansion: 'list'
            filter: false
            persistAuthorization: true

    openapi:
        contact:
            name: 'DiaSys Support'
            email: '<EMAIL>'

    # HTTP Cache configuration
    http_cache:
        public: ~
        invalidation:
            enabled: true
            varnish_urls: []
            request_options: []
            purger: 'api_platform.http_cache.purger.varnish.ban'

    # Mercure integration (disabled)
    mercure:
        enabled: false

    # Messenger integration (disabled)
    messenger:
        enabled: false

    # Elasticsearch integration (disabled)
    elasticsearch:
        enabled: false
        hosts: []

    # Doctrine integration
    doctrine:
        enabled: true

    # GraphQL integration (disabled)
    graphql:
        enabled: false
        default_ide: 'graphiql'
        introspection:
            enabled: true
        nesting_separator: '_'
        collection:
            pagination:
                enabled: true

    # Exception to status code mapping
    exception_to_status:
        # The 4 following handlers are registered by default, keep those lines to prevent unexpected side effects
        Symfony\Component\Serializer\Exception\ExceptionInterface: 400
        ApiPlatform\Exception\InvalidArgumentException: !php/const Symfony\Component\HttpFoundation\Response::HTTP_BAD_REQUEST
        ApiPlatform\ParameterValidator\Exception\ValidationExceptionInterface: 400
        Doctrine\ORM\OptimisticLockException: 409

        # Validation exception
        ApiPlatform\Validator\Exception\ValidationException: !php/const Symfony\Component\HttpFoundation\Response::HTTP_UNPROCESSABLE_ENTITY

        # Custom mappings for healthcare domain
        Symfony\Component\Security\Core\Exception\AccessDeniedException: !php/const Symfony\Component\HttpFoundation\Response::HTTP_FORBIDDEN
        Symfony\Component\Security\Core\Exception\AuthenticationException: !php/const Symfony\Component\HttpFoundation\Response::HTTP_UNAUTHORIZED
        Doctrine\ORM\EntityNotFoundException: !php/const Symfony\Component\HttpFoundation\Response::HTTP_NOT_FOUND
        Doctrine\DBAL\Exception\UniqueConstraintViolationException: !php/const Symfony\Component\HttpFoundation\Response::HTTP_CONFLICT
        Doctrine\DBAL\Exception\ForeignKeyConstraintViolationException: !php/const Symfony\Component\HttpFoundation\Response::HTTP_CONFLICT

        # Custom healthcare exceptions
        App\Exception\HealthcareApiException: 500
        App\Exception\ResourceNotFoundException: 404