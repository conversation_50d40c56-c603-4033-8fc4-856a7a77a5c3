# Doctrine ORM Filters
services:
    # User filters
    user.search_filter:
        parent: 'api_platform.doctrine.orm.search_filter'
        arguments: [{ email: 'ipartial', phone: 'ipartial', status: 'exact', profile: 'exact', doctor: 'exact', patient: 'exact' }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    user.order_filter:
        parent: 'api_platform.doctrine.orm.order_filter'
        arguments: [{ email: ~, phone: ~, status: ~, createdAt: ~, updatedAt: ~ }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    user.date_filter:
        parent: 'api_platform.doctrine.orm.date_filter'
        arguments: [{ lastLoginAt: ~, createdAt: ~, updatedAt: ~ }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    # UserProfile filters
    user_profile.search_filter:
        parent: 'api_platform.doctrine.orm.search_filter'
        arguments: [{ firstName: 'ipartial', lastName: 'ipartial' }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    user_profile.order_filter:
        parent: 'api_platform.doctrine.orm.order_filter'
        arguments: [{ firstName: ~, lastName: ~, createdAt: ~, updatedAt: ~ }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    # UserVerification filters
    user_verification.search_filter:
        parent: 'api_platform.doctrine.orm.search_filter'
        arguments: [{ user: 'exact', verificationType: 'exact', verificationMethod: 'exact', token: 'exact' }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    user_verification.date_filter:
        parent: 'api_platform.doctrine.orm.date_filter'
        arguments: [{ expiresAt: ~, verifiedAt: ~, createdAt: ~, updatedAt: ~ }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    # Doctor filters
    doctor.search_filter:
        parent: 'api_platform.doctrine.orm.search_filter'
        arguments: [{ firstName: 'ipartial', lastName: 'ipartial', registrationNumber: 'exact', specialization: 'ipartial', degree: 'exact', status: 'exact' }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    doctor.order_filter:
        parent: 'api_platform.doctrine.orm.order_filter'
        arguments: [{ firstName: ~, lastName: ~, registrationNumber: ~, specialization: ~, degree: ~, consultationFee: ~, status: ~, createdAt: ~, updatedAt: ~ }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    # Patient filters
    patient.search_filter:
        parent: 'api_platform.doctrine.orm.search_filter'
        arguments: [{ firstName: 'ipartial', lastName: 'ipartial', patientId: 'exact', district: 'ipartial', village: 'ipartial', phone: 'exact', email: 'exact', bloodType: 'exact' }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    patient.order_filter:
        parent: 'api_platform.doctrine.orm.order_filter'
        arguments: [{ firstName: ~, lastName: ~, patientId: ~, district: ~, village: ~, dateOfBirth: ~, createdAt: ~, updatedAt: ~ }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    # Role filters
    role.search_filter:
        parent: 'api_platform.doctrine.orm.search_filter'
        arguments: [{ name: 'ipartial', displayName: 'ipartial', isSystemRole: 'exact' }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    role.order_filter:
        parent: 'api_platform.doctrine.orm.order_filter'
        arguments: [{ name: ~, displayName: ~, isSystemRole: ~, createdAt: ~, updatedAt: ~ }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    # Permission filters
    permission.search_filter:
        parent: 'api_platform.doctrine.orm.search_filter'
        arguments: [{ name: 'ipartial', displayName: 'ipartial', resource: 'ipartial', action: 'exact', isSystemPermission: 'exact' }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    permission.order_filter:
        parent: 'api_platform.doctrine.orm.order_filter'
        arguments: [{ name: ~, displayName: ~, resource: ~, action: ~, isSystemPermission: ~, createdAt: ~, updatedAt: ~ }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    # UserRole filters
    user_role.search_filter:
        parent: 'api_platform.doctrine.orm.search_filter'
        arguments: [{ user: 'exact', role: 'exact', assignedBy: 'exact' }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    user_role.date_filter:
        parent: 'api_platform.doctrine.orm.date_filter'
        arguments: [{ assignedAt: ~, createdAt: ~, updatedAt: ~ }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    # RolePermission filters
    role_permission.search_filter:
        parent: 'api_platform.doctrine.orm.search_filter'
        arguments: [{ role: 'exact', permission: 'exact', grantedBy: 'exact' }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    role_permission.date_filter:
        parent: 'api_platform.doctrine.orm.date_filter'
        arguments: [{ grantedAt: ~, createdAt: ~, updatedAt: ~ }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    # User2FASetting filters
    user_2fa.search_filter:
        parent: 'api_platform.doctrine.orm.search_filter'
        arguments: [{ user: 'exact', method: 'exact', enabledAt: 'exact' }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    user_2fa.date_filter:
        parent: 'api_platform.doctrine.orm.date_filter'
        arguments: [{ enabledAt: ~, lastUsedAt: ~, createdAt: ~, updatedAt: ~ }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    # AuditLog filters
    audit_log.search_filter:
        parent: 'api_platform.doctrine.orm.search_filter'
        arguments: [{ user: 'exact', action: 'exact', category: 'exact', resourceType: 'ipartial', resourceId: 'exact', success: 'exact' }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    audit_log.date_filter:
        parent: 'api_platform.doctrine.orm.date_filter'
        arguments: [{ timestamp: ~, createdAt: ~ }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    # SecurityEvent filters
    security_event.search_filter:
        parent: 'api_platform.doctrine.orm.search_filter'
        arguments: [{ eventType: 'exact', category: 'exact', severity: 'exact', user: 'exact', ip_address: 'exact', isResolved: 'exact', correlationId: 'exact' }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    security_event.date_filter:
        parent: 'api_platform.doctrine.orm.date_filter'
        arguments: [{ timestamp: ~, resolvedAt: ~, createdAt: ~, updatedAt: ~ }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    # SystemConfiguration filters
    system_config.search_filter:
        parent: 'api_platform.doctrine.orm.search_filter'
        arguments: [{ configKey: 'ipartial', category: 'ipartial', isSystemSetting: 'exact' }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    system_config.order_filter:
        parent: 'api_platform.doctrine.orm.order_filter'
        arguments: [{ configKey: ~, category: ~, isSystemSetting: ~, createdAt: ~, updatedAt: ~ }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    # ConfigurationHistory filters
    config_history.search_filter:
        parent: 'api_platform.doctrine.orm.search_filter'
        arguments: [{ configKey: 'ipartial', changedBy: 'exact' }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    config_history.date_filter:
        parent: 'api_platform.doctrine.orm.date_filter'
        arguments: [{ timestamp: ~, createdAt: ~ }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    # UserNotification filters
    user_notification.search_filter:
        parent: 'api_platform.doctrine.orm.search_filter'
        arguments: [{ user: 'exact', type: 'exact', priority: 'exact', channel: 'exact', isRead: 'exact', title: 'ipartial' }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false

    user_notification.date_filter:
        parent: 'api_platform.doctrine.orm.date_filter'
        arguments: [{ sentAt: ~, expiresAt: ~, readAt: ~, createdAt: ~, updatedAt: ~ }]
        tags: ['api_platform.filter']
        autowire: false
        autoconfigure: false
        public: false