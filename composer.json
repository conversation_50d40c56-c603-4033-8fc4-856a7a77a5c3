{"name": "diasys/diasys", "type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "version": "3.0.0-beta", "require": {"php": ">=8.2", "ext-ctype": "*", "ext-iconv": "*", "api-platform/doctrine-orm": "^4.1", "api-platform/symfony": "^4.1", "aws/aws-sdk-php": "^3.356", "doctrine/dbal": "^3", "doctrine/doctrine-bundle": "^2.15", "doctrine/doctrine-migrations-bundle": "^3.4", "doctrine/orm": "^3.5", "easycorp/easyadmin-bundle": "^4.24", "gedmo/doctrine-extensions": "^3.20", "league/flysystem": "^3.30", "league/flysystem-aws-s3-v3": "^3.29", "lexik/jwt-authentication-bundle": "^3.1", "nelmio/cors-bundle": "^2.5", "phpdocumentor/reflection-docblock": "^5.6", "phpstan/phpdoc-parser": "^2.3", "predis/predis": "^3.2", "symfony/asset": "7.3.*", "symfony/asset-mapper": "7.3.*", "symfony/console": "7.3.*", "symfony/doctrine-messenger": "7.3.*", "symfony/dotenv": "7.3.*", "symfony/expression-language": "7.3.*", "symfony/flex": "^2", "symfony/form": "7.3.*", "symfony/framework-bundle": "7.3.*", "symfony/http-client": "7.3.*", "symfony/intl": "7.3.*", "symfony/mailer": "7.3.*", "symfony/mercure-bundle": "^0.3.9", "symfony/mime": "7.3.*", "symfony/monolog-bundle": "^3.0", "symfony/notifier": "7.3.*", "symfony/process": "7.3.*", "symfony/property-access": "7.3.*", "symfony/property-info": "7.3.*", "symfony/rate-limiter": "7.3.*", "symfony/resend-mailer": "7.3.*", "symfony/runtime": "7.3.*", "symfony/security-bundle": "7.3.*", "symfony/sendgrid-mailer": "7.3.*", "symfony/serializer": "7.3.*", "symfony/stimulus-bundle": "^2.30", "symfony/string": "7.3.*", "symfony/translation": "7.3.*", "symfony/twig-bundle": "7.3.*", "symfony/ux-turbo": "^2.30", "symfony/validator": "7.3.*", "symfony/web-link": "7.3.*", "symfony/workflow": "7.3.*", "symfony/yaml": "7.3.*", "twig/extra-bundle": "^2.12|^3.0", "twig/twig": "^2.12|^3.0"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "bump-after-update": true, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd", "importmap:install": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.3.*", "docker": true}}, "require-dev": {"doctrine/doctrine-fixtures-bundle": "^4.1", "phpunit/phpunit": "^12.3", "symfony/browser-kit": "7.3.*", "symfony/css-selector": "7.3.*", "symfony/debug-bundle": "7.3.*", "symfony/maker-bundle": "^1.0", "symfony/stopwatch": "7.3.*", "symfony/web-profiler-bundle": "7.3.*"}}