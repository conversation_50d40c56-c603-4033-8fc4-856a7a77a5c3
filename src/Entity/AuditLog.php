<?php

namespace App\Entity;

use App\Repository\AuditLogRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use ApiPlatform\Metadata\ApiResource;

#[ORM\Entity(repositoryClass: AuditLogRepository::class)]
#[ORM\Table(name: 'audit_log')]
#[ORM\HasLifecycleCallbacks]
#[ORM\Index(name: 'idx_audit_log_user_id', columns: ['user_id'])]
#[ORM\Index(name: 'idx_audit_log_action', columns: ['action'])]
#[ORM\Index(name: 'idx_audit_log_category', columns: ['category'])]
#[ORM\Index(name: 'idx_audit_log_resource_type', columns: ['resource_type'])]
#[ORM\Index(name: 'idx_audit_log_resource_id', columns: ['resource_id'])]
#[ORM\Index(name: 'idx_audit_log_timestamp', columns: ['timestamp'])]
#[ORM\Index(name: 'idx_audit_log_success', columns: ['success'])]
#[ApiResource(
    normalizationContext: ['groups' => ['audit_log:read']],
    security: "is_granted('ROLE_ADMIN')",
    securityMessage: 'Access denied.'
)]
class AuditLog
{
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'SEQUENCE')]
    #[ORM\SequenceGenerator(sequenceName: 'audit_log_id_seq', allocationSize: 1, initialValue: 1)]
    #[ORM\Column(type: Types::BIGINT)]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(name: 'user_id', referencedColumnName: 'id', onDelete: 'SET NULL')]
    #[Groups(['audit_log:read'])]
    private ?User $user = null;

    #[ORM\Column(type: Types::STRING, length: 50)]
    #[Groups(['audit_log:read'])]
    private ?string $action = null;

    #[ORM\Column(type: Types::STRING, length: 50)]
    #[Groups(['audit_log:read'])]
    private ?string $category = null;

    #[ORM\Column(name: 'resource_type', type: Types::STRING, length: 100)]
    #[Groups(['audit_log:read'])]
    private ?string $resourceType = null;

    #[ORM\Column(name: 'resource_id', type: Types::INTEGER, nullable: true)]
    #[Groups(['audit_log:read'])]
    private ?int $resourceId = null;

    #[ORM\Column(name: 'old_values', type: Types::JSON, nullable: true)]
    #[Groups(['audit_log:read'])]
    private ?array $oldValues = null;

    #[ORM\Column(name: 'new_values', type: Types::JSON, nullable: true)]
    #[Groups(['audit_log:read'])]
    private ?array $newValues = null;

    #[ORM\Column(name: 'ip_address', type: Types::STRING, length: 45, nullable: true)]
    #[Groups(['audit_log:read'])]
    private ?string $ipAddress = null;

    #[ORM\Column(name: 'user_agent', type: Types::TEXT, nullable: true)]
    #[Groups(['audit_log:read'])]
    private ?string $userAgent = null;

    #[ORM\Column(name: 'session_id', type: Types::STRING, length: 255, nullable: true)]
    #[Groups(['audit_log:read'])]
    private ?string $sessionId = null;

    #[ORM\Column(type: Types::STRING, length: 20, nullable: true)]
    #[Groups(['audit_log:read'])]
    private ?string $source = null;

    #[ORM\Column(type: Types::BOOLEAN)]
    #[Groups(['audit_log:read'])]
    private bool $success = true;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    #[Groups(['audit_log:read'])]
    private ?\DateTimeInterface $timestamp = null;

    #[ORM\Column(name: 'created_at', type: Types::DATETIME_MUTABLE)]
    #[Groups(['audit_log:read'])]
    private ?\DateTimeInterface $createdAt = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getAction(): ?string
    {
        return $this->action;
    }

    public function setAction(string $action): self
    {
        $this->action = $action;

        return $this;
    }

    public function getCategory(): ?string
    {
        return $this->category;
    }

    public function setCategory(string $category): self
    {
        $this->category = $category;

        return $this;
    }

    public function getResourceType(): ?string
    {
        return $this->resourceType;
    }

    public function setResourceType(string $resourceType): self
    {
        $this->resourceType = $resourceType;

        return $this;
    }

    public function getResourceId(): ?int
    {
        return $this->resourceId;
    }

    public function setResourceId(?int $resourceId): self
    {
        $this->resourceId = $resourceId;

        return $this;
    }

    public function getOldValues(): ?array
    {
        return $this->oldValues;
    }

    public function setOldValues(?array $oldValues): self
    {
        $this->oldValues = $oldValues;

        return $this;
    }

    public function getNewValues(): ?array
    {
        return $this->newValues;
    }

    public function setNewValues(?array $newValues): self
    {
        $this->newValues = $newValues;

        return $this;
    }

    public function getIpAddress(): ?string
    {
        return $this->ipAddress;
    }

    public function setIpAddress(?string $ipAddress): self
    {
        $this->ipAddress = $ipAddress;

        return $this;
    }

    public function getUserAgent(): ?string
    {
        return $this->userAgent;
    }

    public function setUserAgent(?string $userAgent): self
    {
        $this->userAgent = $userAgent;

        return $this;
    }

    public function getSessionId(): ?string
    {
        return $this->sessionId;
    }

    public function setSessionId(?string $sessionId): self
    {
        $this->sessionId = $sessionId;

        return $this;
    }

    public function getSource(): ?string
    {
        return $this->source;
    }

    public function setSource(?string $source): self
    {
        $this->source = $source;

        return $this;
    }

    public function isSuccess(): bool
    {
        return $this->success;
    }

    public function setSuccess(bool $success): self
    {
        $this->success = $success;

        return $this;
    }

    public function getTimestamp(): ?\DateTimeInterface
    {
        return $this->timestamp;
    }

    public function setTimestamp(\DateTimeInterface $timestamp): self
    {
        $this->timestamp = $timestamp;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    #[ORM\PrePersist]
    public function onPrePersist(): void
    {
        $this->createdAt = new \DateTimeImmutable();
        if ($this->timestamp === null) {
            $this->timestamp = new \DateTimeImmutable();
        }
    }
}