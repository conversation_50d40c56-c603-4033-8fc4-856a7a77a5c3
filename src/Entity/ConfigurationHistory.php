<?php

namespace App\Entity;

use App\Repository\ConfigurationHistoryRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use ApiPlatform\Metadata\ApiResource;

#[ORM\Entity(repositoryClass: ConfigurationHistoryRepository::class)]
#[ORM\Table(name: 'configuration_history')]
#[ORM\HasLifecycleCallbacks]
#[ORM\Index(name: 'idx_configuration_history_config_key', columns: ['config_key'])]
#[ORM\Index(name: 'idx_configuration_history_changed_by', columns: ['changed_by'])]
#[ORM\Index(name: 'idx_configuration_history_timestamp', columns: ['timestamp'])]
#[ApiResource(
    normalizationContext: ['groups' => ['config_history:read']],
    security: "is_granted('ROLE_ADMIN')",
    securityMessage: 'Access denied.'
)]
class ConfigurationHistory
{
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'SEQUENCE')]
    #[ORM\SequenceGenerator(sequenceName: 'configuration_history_id_seq', allocationSize: 1, initialValue: 1)]
    #[ORM\Column(type: Types::BIGINT)]
    private ?int $id = null;

    #[ORM\Column(name: 'config_key', type: Types::STRING, length: 255)]
    #[Groups(['config_history:read'])]
    private ?string $configKey = null;

    #[ORM\Column(name: 'old_value', type: Types::TEXT, nullable: true)]
    #[Groups(['config_history:read'])]
    private ?string $oldValue = null;

    #[ORM\Column(name: 'new_value', type: Types::TEXT, nullable: true)]
    #[Groups(['config_history:read'])]
    private ?string $newValue = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(name: 'changed_by', referencedColumnName: 'id', nullable: false)]
    #[Groups(['config_history:read'])]
    private ?User $changedBy = null;

    #[ORM\Column(name: 'change_reason', type: Types::TEXT, nullable: true)]
    #[Groups(['config_history:read'])]
    private ?string $changeReason = null;

    #[ORM\Column(name: 'ip_address', type: Types::STRING, length: 45, nullable: true)]
    #[Groups(['config_history:read'])]
    private ?string $ipAddress = null;

    #[ORM\Column(name: 'user_agent', type: Types::TEXT, nullable: true)]
    #[Groups(['config_history:read'])]
    private ?string $userAgent = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    #[Groups(['config_history:read'])]
    private ?\DateTimeInterface $timestamp = null;

    #[ORM\Column(name: 'created_at', type: Types::DATETIME_MUTABLE)]
    #[Groups(['config_history:read'])]
    private ?\DateTimeInterface $createdAt = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getConfigKey(): ?string
    {
        return $this->configKey;
    }

    public function setConfigKey(string $configKey): self
    {
        $this->configKey = $configKey;

        return $this;
    }

    public function getOldValue(): ?string
    {
        return $this->oldValue;
    }

    public function setOldValue(?string $oldValue): self
    {
        $this->oldValue = $oldValue;

        return $this;
    }

    public function getNewValue(): ?string
    {
        return $this->newValue;
    }

    public function setNewValue(?string $newValue): self
    {
        $this->newValue = $newValue;

        return $this;
    }

    public function getChangedBy(): ?User
    {
        return $this->changedBy;
    }

    public function setChangedBy(?User $changedBy): self
    {
        $this->changedBy = $changedBy;

        return $this;
    }

    public function getChangeReason(): ?string
    {
        return $this->changeReason;
    }

    public function setChangeReason(?string $changeReason): self
    {
        $this->changeReason = $changeReason;

        return $this;
    }

    public function getIpAddress(): ?string
    {
        return $this->ipAddress;
    }

    public function setIpAddress(?string $ipAddress): self
    {
        $this->ipAddress = $ipAddress;

        return $this;
    }

    public function getUserAgent(): ?string
    {
        return $this->userAgent;
    }

    public function setUserAgent(?string $userAgent): self
    {
        $this->userAgent = $userAgent;

        return $this;
    }

    public function getTimestamp(): ?\DateTimeInterface
    {
        return $this->timestamp;
    }

    public function setTimestamp(\DateTimeInterface $timestamp): self
    {
        $this->timestamp = $timestamp;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    #[ORM\PrePersist]
    public function onPrePersist(): void
    {
        $this->createdAt = new \DateTimeImmutable();
        if ($this->timestamp === null) {
            $this->timestamp = new \DateTimeImmutable();
        }
    }
}