<?php

namespace App\Entity;

use App\Repository\DoctorRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use ApiPlatform\Metadata\ApiResource;

#[ORM\Entity(repositoryClass: DoctorRepository::class)]
#[ORM\Table(name: 'doctors')]
#[ORM\HasLifecycleCallbacks]
#[ORM\Index(name: 'idx_doctors_name', columns: ['last_name', 'first_name'])]
#[ORM\Index(name: 'idx_doctors_registration_number', columns: ['registration_number'])]
#[ORM\Index(name: 'idx_doctors_specialization', columns: ['specialization'])]
#[ORM\Index(name: 'idx_doctors_degree', columns: ['degree'])]
#[ORM\Index(name: 'idx_doctors_consultation_fee', columns: ['consultation_fee'])]
#[ORM\Index(name: 'idx_doctors_status', columns: ['status'])]
#[ApiResource(
    normalizationContext: ['groups' => ['doctor:read']],
    denormalizationContext: ['groups' => ['doctor:write']],
    security: "is_granted('ROLE_ADMIN') or is_granted('ROLE_DOCTOR')",
    securityMessage: 'Access denied.'
)]
class Doctor
{
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'SEQUENCE')]
    #[ORM\SequenceGenerator(sequenceName: 'doctors_id_seq', allocationSize: 1, initialValue: 1)]
    #[ORM\Column(type: Types::BIGINT)]
    private ?int $id = null;

    #[ORM\Column(type: Types::STRING, length: 20, nullable: true)]
    #[Groups(['doctor:read', 'doctor:write'])]
    private ?string $prefix = null;

    #[ORM\Column(name: 'first_name', type: Types::STRING, length: 255)]
    #[Groups(['doctor:read', 'doctor:write'])]
    private ?string $firstName = null;

    #[ORM\Column(name: 'last_name', type: Types::STRING, length: 255)]
    #[Groups(['doctor:read', 'doctor:write'])]
    private ?string $lastName = null;

    #[ORM\Column(type: Types::STRING, length: 20, nullable: true)]
    #[Groups(['doctor:read', 'doctor:write'])]
    private ?string $suffix = null;

    #[ORM\Column(name: 'registration_number', type: Types::STRING, length: 255, unique: true)]
    #[Groups(['doctor:read', 'doctor:write'])]
    private ?string $registrationNumber = null;

    #[ORM\Column(name: 'registration_expiry_date', type: Types::DATE_MUTABLE, nullable: true)]
    #[Groups(['doctor:read', 'doctor:write'])]
    private ?\DateTimeInterface $registrationExpiryDate = null;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    #[Groups(['doctor:read', 'doctor:write'])]
    private ?array $education = null;

    #[ORM\Column(type: Types::STRING, length: 100)]
    #[Groups(['doctor:read', 'doctor:write'])]
    private ?string $degree = null;

    #[ORM\Column(type: Types::STRING, length: 255)]
    #[Groups(['doctor:read', 'doctor:write'])]
    private ?string $specialization = null;

    #[ORM\Column(name: 'years_of_experience', type: Types::INTEGER, nullable: true)]
    #[Groups(['doctor:read', 'doctor:write'])]
    private ?int $yearsOfExperience = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    #[Groups(['doctor:read', 'doctor:write'])]
    private ?string $bio = null;

    #[ORM\Column(type: Types::STRING, length: 255, nullable: true)]
    #[Groups(['doctor:read', 'doctor:write'])]
    private ?string $picture = null;

    #[ORM\Column(name: 'consultation_fee', type: Types::DECIMAL, precision: 10, scale: 2, nullable: true)]
    #[Groups(['doctor:read', 'doctor:write'])]
    private ?string $consultationFee = null;

    #[ORM\Column(name: 'availability_schedule', type: Types::JSON, nullable: true)]
    #[Groups(['doctor:read', 'doctor:write'])]
    private ?array $availabilitySchedule = null;

    #[ORM\Column(type: Types::STRING, length: 20)]
    #[Groups(['doctor:read', 'doctor:write'])]
    private string $status = 'active';

    #[ORM\OneToOne(targetEntity: User::class, mappedBy: 'doctor')]
    #[Groups(['doctor:read'])]
    private ?User $user = null;

    #[ORM\Column(name: 'created_at', type: Types::DATETIME_MUTABLE)]
    #[Groups(['doctor:read'])]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(name: 'updated_at', type: Types::DATETIME_MUTABLE)]
    #[Groups(['doctor:read'])]
    private ?\DateTimeInterface $updatedAt = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPrefix(): ?string
    {
        return $this->prefix;
    }

    public function setPrefix(?string $prefix): self
    {
        $this->prefix = $prefix;

        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getSuffix(): ?string
    {
        return $this->suffix;
    }

    public function setSuffix(?string $suffix): self
    {
        $this->suffix = $suffix;

        return $this;
    }

    public function getRegistrationNumber(): ?string
    {
        return $this->registrationNumber;
    }

    public function setRegistrationNumber(string $registrationNumber): self
    {
        $this->registrationNumber = $registrationNumber;

        return $this;
    }

    public function getRegistrationExpiryDate(): ?\DateTimeInterface
    {
        return $this->registrationExpiryDate;
    }

    public function setRegistrationExpiryDate(?\DateTimeInterface $registrationExpiryDate): self
    {
        $this->registrationExpiryDate = $registrationExpiryDate;

        return $this;
    }

    public function getEducation(): ?array
    {
        return $this->education;
    }

    public function setEducation(?array $education): self
    {
        $this->education = $education;

        return $this;
    }

    public function getDegree(): ?string
    {
        return $this->degree;
    }

    public function setDegree(string $degree): self
    {
        $this->degree = $degree;

        return $this;
    }

    public function getSpecialization(): ?string
    {
        return $this->specialization;
    }

    public function setSpecialization(string $specialization): self
    {
        $this->specialization = $specialization;

        return $this;
    }

    public function getYearsOfExperience(): ?int
    {
        return $this->yearsOfExperience;
    }

    public function setYearsOfExperience(?int $yearsOfExperience): self
    {
        $this->yearsOfExperience = $yearsOfExperience;

        return $this;
    }

    public function getBio(): ?string
    {
        return $this->bio;
    }

    public function setBio(?string $bio): self
    {
        $this->bio = $bio;

        return $this;
    }

    public function getPicture(): ?string
    {
        return $this->picture;
    }

    public function setPicture(?string $picture): self
    {
        $this->picture = $picture;

        return $this;
    }

    public function getConsultationFee(): ?string
    {
        return $this->consultationFee;
    }

    public function setConsultationFee(?string $consultationFee): self
    {
        $this->consultationFee = $consultationFee;

        return $this;
    }

    public function getAvailabilitySchedule(): ?array
    {
        return $this->availabilitySchedule;
    }

    public function setAvailabilitySchedule(?array $availabilitySchedule): self
    {
        $this->availabilitySchedule = $availabilitySchedule;

        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeInterface $updatedAt): self
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    #[ORM\PrePersist]
    public function onPrePersist(): void
    {
        $this->createdAt = new \DateTimeImmutable();
        $this->updatedAt = new \DateTimeImmutable();
    }

    #[ORM\PreUpdate]
    public function onPreUpdate(): void
    {
        $this->updatedAt = new \DateTimeImmutable();
    }

    public function __toString(): string
    {
        $name = '';

        if ($this->prefix) {
            $name .= $this->prefix . ' ';
        }

        $name .= $this->firstName . ' ' . $this->lastName;

        if ($this->suffix) {
            $name .= ' ' . $this->suffix;
        }

        return $name;
    }
}