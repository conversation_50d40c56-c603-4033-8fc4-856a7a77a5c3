<?php

namespace App\Entity;

use App\Repository\SystemConfigurationRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use ApiPlatform\Metadata\ApiResource;

#[ORM\Entity(repositoryClass: SystemConfigurationRepository::class)]
#[ORM\Table(name: 'system_configuration')]
#[ORM\HasLifecycleCallbacks]
#[ORM\Index(name: 'idx_system_configuration_config_key', columns: ['config_key'])]
#[ORM\Index(name: 'idx_system_configuration_category', columns: ['category'])]
#[ORM\Index(name: 'idx_system_configuration_is_system_setting', columns: ['is_system_setting'])]
#[ApiResource(
    normalizationContext: ['groups' => ['system_config:read']],
    denormalizationContext: ['groups' => ['system_config:write']],
    security: "is_granted('ROLE_ADMIN')",
    securityMessage: 'Access denied.'
)]
class SystemConfiguration
{
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'SEQUENCE')]
    #[ORM\SequenceGenerator(sequenceName: 'system_configuration_id_seq', allocationSize: 1, initialValue: 1)]
    #[ORM\Column(type: Types::BIGINT)]
    private ?int $id = null;

    #[ORM\Column(name: 'config_key', type: Types::STRING, length: 255, unique: true)]
    #[Groups(['system_config:read', 'system_config:write'])]
    private ?string $configKey = null;

    #[ORM\Column(name: 'config_value', type: Types::TEXT, nullable: true)]
    #[Groups(['system_config:read', 'system_config:write'])]
    private ?string $configValue = null;

    #[ORM\Column(name: 'config_type', type: Types::STRING, length: 50)]
    #[Groups(['system_config:read', 'system_config:write'])]
    private string $configType = 'string';

    #[ORM\Column(type: Types::STRING, length: 50)]
    #[Groups(['system_config:read', 'system_config:write'])]
    private ?string $category = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    #[Groups(['system_config:read', 'system_config:write'])]
    private ?string $description = null;

    #[ORM\Column(name: 'is_system_setting', type: Types::BOOLEAN)]
    #[Groups(['system_config:read', 'system_config:write'])]
    private bool $isSystemSetting = false;

    #[ORM\Column(name: 'is_editable', type: Types::BOOLEAN)]
    #[Groups(['system_config:read', 'system_config:write'])]
    private bool $isEditable = true;

    #[ORM\Column(name: 'validation_rules', type: Types::JSON, nullable: true)]
    #[Groups(['system_config:read', 'system_config:write'])]
    private ?array $validationRules = null;

    #[ORM\Column(name: 'default_value', type: Types::TEXT, nullable: true)]
    #[Groups(['system_config:read', 'system_config:write'])]
    private ?string $defaultValue = null;

    #[ORM\Column(name: 'created_at', type: Types::DATETIME_MUTABLE)]
    #[Groups(['system_config:read'])]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(name: 'updated_at', type: Types::DATETIME_MUTABLE)]
    #[Groups(['system_config:read'])]
    private ?\DateTimeInterface $updatedAt = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getConfigKey(): ?string
    {
        return $this->configKey;
    }

    public function setConfigKey(string $configKey): self
    {
        $this->configKey = $configKey;

        return $this;
    }

    public function getConfigValue(): ?string
    {
        return $this->configValue;
    }

    public function setConfigValue(?string $configValue): self
    {
        $this->configValue = $configValue;

        return $this;
    }

    public function getConfigType(): string
    {
        return $this->configType;
    }

    public function setConfigType(string $configType): self
    {
        $this->configType = $configType;

        return $this;
    }

    public function getCategory(): ?string
    {
        return $this->category;
    }

    public function setCategory(string $category): self
    {
        $this->category = $category;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function isSystemSetting(): bool
    {
        return $this->isSystemSetting;
    }

    public function setIsSystemSetting(bool $isSystemSetting): self
    {
        $this->isSystemSetting = $isSystemSetting;

        return $this;
    }

    public function isEditable(): bool
    {
        return $this->isEditable;
    }

    public function setIsEditable(bool $isEditable): self
    {
        $this->isEditable = $isEditable;

        return $this;
    }

    public function getValidationRules(): ?array
    {
        return $this->validationRules;
    }

    public function setValidationRules(?array $validationRules): self
    {
        $this->validationRules = $validationRules;

        return $this;
    }

    public function getDefaultValue(): ?string
    {
        return $this->defaultValue;
    }

    public function setDefaultValue(?string $defaultValue): self
    {
        $this->defaultValue = $defaultValue;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeInterface $updatedAt): self
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    #[ORM\PrePersist]
    public function onPrePersist(): void
    {
        $this->createdAt = new \DateTimeImmutable();
        $this->updatedAt = new \DateTimeImmutable();
    }

    #[ORM\PreUpdate]
    public function onPreUpdate(): void
    {
        $this->updatedAt = new \DateTimeImmutable();
    }
}