<?php

namespace App\Entity;

use App\Repository\UserRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Symfony\Component\Serializer\Annotation\Groups;
use ApiPlatform\Metadata\ApiResource;

#[ORM\Entity(repositoryClass: UserRepository::class)]
#[ORM\Table(name: 'users')]
#[ORM\HasLifecycleCallbacks]
#[ORM\Index(name: 'idx_users_email', columns: ['email'])]
#[ORM\Index(name: 'idx_users_phone', columns: ['phone'])]
#[ORM\Index(name: 'idx_users_status', columns: ['status'])]
#[ORM\Index(name: 'idx_users_profile_id', columns: ['profile_id'])]
#[ORM\Index(name: 'idx_users_doctor_id', columns: ['doctor_id'])]
#[ORM\Index(name: 'idx_users_patient_id', columns: ['patient_id'])]
#[ApiResource(
    normalizationContext: ['groups' => ['user:read']],
    denormalizationContext: ['groups' => ['user:write']],
    security: "is_granted('ROLE_ADMIN') or object == user",
    securityMessage: 'Access denied.'
)]
class User implements UserInterface, PasswordAuthenticatedUserInterface
{
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'SEQUENCE')]
    #[ORM\SequenceGenerator(sequenceName: 'users_id_seq', allocationSize: 1, initialValue: 1)]
    #[ORM\Column(type: Types::BIGINT)]
    private ?int $id = null;

    #[ORM\Column(type: Types::STRING, length: 255, unique: true)]
    #[Groups(['user:read', 'user:write', 'user:admin:read', 'user:admin:write'])]
    private ?string $email = null;

    #[ORM\Column(type: Types::STRING, length: 255, nullable: true, unique: true)]
    #[Groups(['user:read', 'user:write', 'user:admin:read', 'user:admin:write'])]
    private ?string $phone = null;

    #[ORM\Column(type: Types::STRING, length: 255)]
    #[Groups(['user:write', 'user:admin:write'])]
    private ?string $password = null;

    #[ORM\Column(type: Types::JSON)]
    #[Groups(['user:read', 'user:admin:read', 'user:admin:write'])]
    private array $roles = [];

    #[ORM\Column(type: Types::STRING, length: 50, options: ['default' => 'pending'])]
    #[Groups(['user:read', 'user:admin:read', 'user:admin:write'])]
    private string $status = 'pending';

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    #[Groups(['user:read', 'user:admin:read'])]
    private ?\DateTimeInterface $lastLoginAt = null;

    #[ORM\OneToOne(targetEntity: UserProfile::class, inversedBy: 'user')]
    #[ORM\JoinColumn(name: 'profile_id', referencedColumnName: 'id', onDelete: 'SET NULL')]
    #[Groups(['user:read', 'user:admin:read', 'user:admin:write'])]
    private ?UserProfile $profile = null;

    #[ORM\OneToOne(targetEntity: Doctor::class, inversedBy: 'user')]
    #[ORM\JoinColumn(name: 'doctor_id', referencedColumnName: 'id', onDelete: 'SET NULL')]
    #[Groups(['user:read', 'user:admin:read'])]
    private ?Doctor $doctor = null;

    #[ORM\OneToOne(targetEntity: Patient::class, inversedBy: 'user')]
    #[ORM\JoinColumn(name: 'patient_id', referencedColumnName: 'id', onDelete: 'SET NULL')]
    #[Groups(['user:read', 'user:admin:read'])]
    private ?Patient $patient = null;

    /**
     * @var Collection<int, UserVerification>
     */
    #[ORM\OneToMany(targetEntity: UserVerification::class, mappedBy: 'user', cascade: ['persist', 'remove'])]
    private Collection $userVerifications;

    #[ORM\OneToOne(targetEntity: User2FASetting::class, mappedBy: 'user', cascade: ['persist', 'remove'])]
    private ?User2FASetting $user2FASetting = null;

    #[ORM\Column(name: 'created_at', type: Types::DATETIME_MUTABLE)]
    #[Groups(['user:read', 'user:admin:read'])]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(name: 'updated_at', type: Types::DATETIME_MUTABLE)]
    #[Groups(['user:read', 'user:admin:read'])]
    private ?\DateTimeInterface $updatedAt = null;

    public function __construct()
    {
        $this->roles = [];
        $this->userVerifications = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getUserIdentifier(): string
    {
        return (string) $this->email;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function setPhone(?string $phone): self
    {
        $this->phone = $phone;

        return $this;
    }

    public function getPassword(): string
    {
        return $this->password;
    }

    public function setPassword(string $password): self
    {
        $this->password = $password;

        return $this;
    }

    public function getRoles(): array
    {
        $roles = $this->roles;
        // guarantee every user at least has ROLE_USER
        $roles[] = 'ROLE_USER';

        return array_unique($roles);
    }

    public function setRoles(array $roles): self
    {
        $this->roles = $roles;

        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getLastLoginAt(): ?\DateTimeInterface
    {
        return $this->lastLoginAt;
    }

    public function setLastLoginAt(?\DateTimeInterface $lastLoginAt): self
    {
        $this->lastLoginAt = $lastLoginAt;

        return $this;
    }

    public function getProfile(): ?UserProfile
    {
        return $this->profile;
    }

    public function setProfile(?UserProfile $profile): self
    {
        $this->profile = $profile;

        return $this;
    }

    public function getDoctor(): ?Doctor
    {
        return $this->doctor;
    }

    public function setDoctor(?Doctor $doctor): self
    {
        $this->doctor = $doctor;

        return $this;
    }

    public function getPatient(): ?Patient
    {
        return $this->patient;
    }

    public function setPatient(?Patient $patient): self
    {
        $this->patient = $patient;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeInterface $updatedAt): self
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * @see UserInterface
     */
    public function eraseCredentials(): void
    {
        // If you store any temporary, sensitive data on the user, clear it here
        // $this->plainPassword = null;
    }

    #[ORM\PrePersist]
    public function onPrePersist(): void
    {
        $this->createdAt = new \DateTimeImmutable();
        $this->updatedAt = new \DateTimeImmutable();
    }

    #[ORM\PreUpdate]
    public function onPreUpdate(): void
    {
        $this->updatedAt = new \DateTimeImmutable();
    }

    public function __toString(): string
    {
        return $this->email;
    }
}