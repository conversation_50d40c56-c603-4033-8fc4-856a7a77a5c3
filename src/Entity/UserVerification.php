<?php

namespace App\Entity;

use App\Repository\UserVerificationRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use ApiPlatform\Metadata\ApiResource;

#[ORM\Entity(repositoryClass: UserVerificationRepository::class)]
#[ORM\Table(name: 'user_verifications')]
#[ORM\HasLifecycleCallbacks]
#[ORM\Index(name: 'idx_user_verifications_user_id', columns: ['user_id'])]
#[ORM\Index(name: 'idx_user_verifications_verification_type', columns: ['verification_type'])]
#[ORM\Index(name: 'idx_user_verifications_verification_method', columns: ['verification_method'])]
#[ORM\Index(name: 'idx_user_verifications_token', columns: ['token'])]
#[ORM\Index(name: 'idx_user_verifications_expires_at', columns: ['expires_at'])]
#[ORM\Index(name: 'idx_user_verifications_verified_at', columns: ['verified_at'])]
#[ORM\Index(name: 'idx_user_verifications_ip_address', columns: ['ip_address'])]
#[ApiResource(
    normalizationContext: ['groups' => ['user_verification:read']],
    denormalizationContext: ['groups' => ['user_verification:write']],
    security: "is_granted('ROLE_ADMIN') or object.user == user",
    securityMessage: 'Access denied.'
)]
class UserVerification
{
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'SEQUENCE')]
    #[ORM\SequenceGenerator(sequenceName: 'user_verifications_id_seq', allocationSize: 1, initialValue: 1)]
    #[ORM\Column(type: Types::BIGINT)]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: User::class, inversedBy: 'userVerifications')]
    #[ORM\JoinColumn(name: 'user_id', referencedColumnName: 'id', onDelete: 'CASCADE')]
    #[Groups(['user_verification:read', 'user_verification:write'])]
    private ?User $user = null;

    #[ORM\Column(name: 'verification_type', type: Types::STRING, length: 50)]
    #[Groups(['user_verification:read', 'user_verification:write'])]
    private ?string $verificationType = null;

    #[ORM\Column(name: 'verification_method', type: Types::STRING, length: 50, nullable: true)]
    #[Groups(['user_verification:read', 'user_verification:write'])]
    private ?string $verificationMethod = null;

    #[ORM\Column(type: Types::STRING, length: 255, unique: true)]
    #[Groups(['user_verification:read', 'user_verification:write'])]
    private ?string $token = null;

    #[ORM\Column(name: 'expires_at', type: Types::DATETIME_MUTABLE)]
    #[Groups(['user_verification:read'])]
    private ?\DateTimeInterface $expiresAt = null;

    #[ORM\Column(name: 'verified_at', type: Types::DATETIME_MUTABLE, nullable: true)]
    #[Groups(['user_verification:read'])]
    private ?\DateTimeInterface $verifiedAt = null;

    #[ORM\Column(name: 'ip_address', type: Types::STRING, length: 45, nullable: true)]
    #[Groups(['user_verification:read'])]
    private ?string $ipAddress = null;

    #[ORM\Column(name: 'user_agent', type: Types::TEXT, nullable: true)]
    #[Groups(['user_verification:read'])]
    private ?string $userAgent = null;

    #[ORM\Column(name: 'created_at', type: Types::DATETIME_MUTABLE)]
    #[Groups(['user_verification:read'])]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(name: 'updated_at', type: Types::DATETIME_MUTABLE)]
    #[Groups(['user_verification:read'])]
    private ?\DateTimeInterface $updatedAt = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getVerificationType(): ?string
    {
        return $this->verificationType;
    }

    public function setVerificationType(string $verificationType): self
    {
        $this->verificationType = $verificationType;

        return $this;
    }

    public function getVerificationMethod(): ?string
    {
        return $this->verificationMethod;
    }

    public function setVerificationMethod(?string $verificationMethod): self
    {
        $this->verificationMethod = $verificationMethod;

        return $this;
    }

    public function getToken(): ?string
    {
        return $this->token;
    }

    public function setToken(string $token): self
    {
        $this->token = $token;

        return $this;
    }

    public function getExpiresAt(): ?\DateTimeInterface
    {
        return $this->expiresAt;
    }

    public function setExpiresAt(\DateTimeInterface $expiresAt): self
    {
        $this->expiresAt = $expiresAt;

        return $this;
    }

    public function getVerifiedAt(): ?\DateTimeInterface
    {
        return $this->verifiedAt;
    }

    public function setVerifiedAt(?\DateTimeInterface $verifiedAt): self
    {
        $this->verifiedAt = $verifiedAt;

        return $this;
    }

    public function getIpAddress(): ?string
    {
        return $this->ipAddress;
    }

    public function setIpAddress(?string $ipAddress): self
    {
        $this->ipAddress = $ipAddress;

        return $this;
    }

    public function getUserAgent(): ?string
    {
        return $this->userAgent;
    }

    public function setUserAgent(?string $userAgent): self
    {
        $this->userAgent = $userAgent;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeInterface $updatedAt): self
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    #[ORM\PrePersist]
    public function onPrePersist(): void
    {
        $this->createdAt = new \DateTimeImmutable();
        $this->updatedAt = new \DateTimeImmutable();
    }

    #[ORM\PreUpdate]
    public function onPreUpdate(): void
    {
        $this->updatedAt = new \DateTimeImmutable();
    }
}