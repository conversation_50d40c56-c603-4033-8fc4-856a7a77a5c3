<?php

namespace App\Entity;

use App\Repository\PatientRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use ApiPlatform\Metadata\ApiResource;

#[ORM\Entity(repositoryClass: PatientRepository::class)]
#[ORM\Table(name: 'patients')]
#[ORM\HasLifecycleCallbacks]
#[ORM\Index(name: 'idx_patients_name', columns: ['last_name', 'first_name'])]
#[ORM\Index(name: 'idx_patients_patient_id', columns: ['patient_id'])]
#[ORM\Index(name: 'idx_patients_district', columns: ['district'])]
#[ORM\Index(name: 'idx_patients_village', columns: ['village'])]
#[ApiResource(
    normalizationContext: ['groups' => ['patient:read']],
    denormalizationContext: ['groups' => ['patient:write']],
    security: "is_granted('ROLE_ADMIN') or is_granted('ROLE_DOCTOR') or object.user == user",
    securityMessage: 'Access denied.'
)]
class Patient
{
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'SEQUENCE')]
    #[ORM\SequenceGenerator(sequenceName: 'patients_id_seq', allocationSize: 1, initialValue: 1)]
    #[ORM\Column(type: Types::BIGINT)]
    private ?int $id = null;

    #[ORM\Column(name: 'patient_id', type: Types::STRING, length: 50, unique: true)]
    #[Groups(['patient:read', 'patient:write'])]
    private ?string $patientId = null;

    #[ORM\Column(name: 'first_name', type: Types::STRING, length: 255)]
    #[Groups(['patient:read', 'patient:write'])]
    private ?string $firstName = null;

    #[ORM\Column(name: 'last_name', type: Types::STRING, length: 255)]
    #[Groups(['patient:read', 'patient:write'])]
    private ?string $lastName = null;

    #[ORM\Column(name: 'date_of_birth', type: Types::DATE_MUTABLE)]
    #[Groups(['patient:read', 'patient:write'])]
    private ?\DateTimeInterface $dateOfBirth = null;

    #[ORM\Column(type: Types::STRING, length: 50)]
    #[Groups(['patient:read', 'patient:write'])]
    private ?string $gender = null;

    #[ORM\Column(type: Types::STRING, length: 20)]
    #[Groups(['patient:read', 'patient:write'])]
    private ?string $phone = null;

    #[ORM\Column(type: Types::STRING, length: 255, nullable: true)]
    #[Groups(['patient:read', 'patient:write'])]
    private ?string $email = null;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    #[Groups(['patient:read', 'patient:write'])]
    private ?array $address = null;

    #[ORM\Column(type: Types::STRING, length: 255, nullable: true)]
    #[Groups(['patient:read', 'patient:write'])]
    private ?string $district = null;

    #[ORM\Column(type: Types::STRING, length: 255, nullable: true)]
    #[Groups(['patient:read', 'patient:write'])]
    private ?string $village = null;

    #[ORM\Column(name: 'postal_code', type: Types::STRING, length: 20, nullable: true)]
    #[Groups(['patient:read', 'patient:write'])]
    private ?string $postalCode = null;

    #[ORM\Column(name: 'emergency_contact_name', type: Types::STRING, length: 255)]
    #[Groups(['patient:read', 'patient:write'])]
    private ?string $emergencyContactName = null;

    #[ORM\Column(name: 'emergency_contact_phone', type: Types::STRING, length: 20)]
    #[Groups(['patient:read', 'patient:write'])]
    private ?string $emergencyContactPhone = null;

    #[ORM\Column(name: 'blood_type', type: Types::STRING, length: 10, nullable: true)]
    #[Groups(['patient:read', 'patient:write'])]
    private ?string $bloodType = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 5, scale: 2, nullable: true)]
    #[Groups(['patient:read', 'patient:write'])]
    private ?string $height = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 5, scale: 2, nullable: true)]
    #[Groups(['patient:read', 'patient:write'])]
    private ?string $weight = null;

    #[ORM\OneToOne(targetEntity: User::class, mappedBy: 'patient')]
    #[Groups(['patient:read'])]
    private ?User $user = null;

    #[ORM\Column(name: 'created_at', type: Types::DATETIME_MUTABLE)]
    #[Groups(['patient:read'])]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(name: 'updated_at', type: Types::DATETIME_MUTABLE)]
    #[Groups(['patient:read'])]
    private ?\DateTimeInterface $updatedAt = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPatientId(): ?string
    {
        return $this->patientId;
    }

    public function setPatientId(string $patientId): self
    {
        $this->patientId = $patientId;

        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getDateOfBirth(): ?\DateTimeInterface
    {
        return $this->dateOfBirth;
    }

    public function setDateOfBirth(\DateTimeInterface $dateOfBirth): self
    {
        $this->dateOfBirth = $dateOfBirth;

        return $this;
    }

    public function getGender(): ?string
    {
        return $this->gender;
    }

    public function setGender(string $gender): self
    {
        $this->gender = $gender;

        return $this;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function setPhone(string $phone): self
    {
        $this->phone = $phone;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getAddress(): ?array
    {
        return $this->address;
    }

    public function setAddress(?array $address): self
    {
        $this->address = $address;

        return $this;
    }

    public function getDistrict(): ?string
    {
        return $this->district;
    }

    public function setDistrict(?string $district): self
    {
        $this->district = $district;

        return $this;
    }

    public function getVillage(): ?string
    {
        return $this->village;
    }

    public function setVillage(?string $village): self
    {
        $this->village = $village;

        return $this;
    }

    public function getPostalCode(): ?string
    {
        return $this->postalCode;
    }

    public function setPostalCode(?string $postalCode): self
    {
        $this->postalCode = $postalCode;

        return $this;
    }

    public function getEmergencyContactName(): ?string
    {
        return $this->emergencyContactName;
    }

    public function setEmergencyContactName(string $emergencyContactName): self
    {
        $this->emergencyContactName = $emergencyContactName;

        return $this;
    }

    public function getEmergencyContactPhone(): ?string
    {
        return $this->emergencyContactPhone;
    }

    public function setEmergencyContactPhone(string $emergencyContactPhone): self
    {
        $this->emergencyContactPhone = $emergencyContactPhone;

        return $this;
    }

    public function getBloodType(): ?string
    {
        return $this->bloodType;
    }

    public function setBloodType(?string $bloodType): self
    {
        $this->bloodType = $bloodType;

        return $this;
    }

    public function getHeight(): ?string
    {
        return $this->height;
    }

    public function setHeight(?string $height): self
    {
        $this->height = $height;

        return $this;
    }

    public function getWeight(): ?string
    {
        return $this->weight;
    }

    public function setWeight(?string $weight): self
    {
        $this->weight = $weight;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeInterface $updatedAt): self
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    #[ORM\PrePersist]
    public function onPrePersist(): void
    {
        $this->createdAt = new \DateTimeImmutable();
        $this->updatedAt = new \DateTimeImmutable();
    }

    #[ORM\PreUpdate]
    public function onPreUpdate(): void
    {
        $this->updatedAt = new \DateTimeImmutable();
    }

    public function __toString(): string
    {
        return $this->firstName . ' ' . $this->lastName . ' (' . $this->patientId . ')';
    }
}