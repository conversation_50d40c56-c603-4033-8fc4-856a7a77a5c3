<?php

namespace App\Entity;

use App\Repository\User2FASettingRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use ApiPlatform\Metadata\ApiResource;

#[ORM\Entity(repositoryClass: User2FASettingRepository::class)]
#[ORM\Table(name: 'user_2fa_settings')]
#[ORM\HasLifecycleCallbacks]
#[ORM\Index(name: 'idx_user_2fa_settings_user_id', columns: ['user_id'])]
#[ORM\Index(name: 'idx_user_2fa_settings_enabled_at', columns: ['enabled_at'])]
#[ApiResource(
    normalizationContext: ['groups' => ['user_2fa:read']],
    denormalizationContext: ['groups' => ['user_2fa:write']],
    security: "is_granted('ROLE_ADMIN') or object.user == user",
    securityMessage: 'Access denied.'
)]
class User2FASetting
{
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'SEQUENCE')]
    #[ORM\SequenceGenerator(sequenceName: 'user_2fa_settings_id_seq', allocationSize: 1, initialValue: 1)]
    #[ORM\Column(type: Types::BIGINT)]
    private ?int $id = null;

    #[ORM\OneToOne(targetEntity: User::class, inversedBy: 'user2FASetting')]
    #[ORM\JoinColumn(name: 'user_id', referencedColumnName: 'id', onDelete: 'CASCADE', unique: true)]
    #[Groups(['user_2fa:read'])]
    private ?User $user = null;

    #[ORM\Column(name: 'secret_key', type: Types::STRING, length: 255)]
    #[Groups(['user_2fa:read', 'user_2fa:write'])]
    private ?string $secretKey = null;

    #[ORM\Column(name: 'backup_codes', type: Types::JSON, nullable: true)]
    #[Groups(['user_2fa:read', 'user_2fa:write'])]
    private ?array $backupCodes = null;

    #[ORM\Column(name: 'enabled_at', type: Types::DATETIME_MUTABLE, nullable: true)]
    #[Groups(['user_2fa:read'])]
    private ?\DateTimeInterface $enabledAt = null;

    #[ORM\Column(name: 'last_used_at', type: Types::DATETIME_MUTABLE, nullable: true)]
    #[Groups(['user_2fa:read'])]
    private ?\DateTimeInterface $lastUsedAt = null;

    #[ORM\Column(type: Types::STRING, length: 20)]
    #[Groups(['user_2fa:read', 'user_2fa:write'])]
    private string $method = 'totp';

    #[ORM\Column(name: 'phone_number', type: Types::STRING, length: 20, nullable: true)]
    #[Groups(['user_2fa:read', 'user_2fa:write'])]
    private ?string $phoneNumber = null;

    #[ORM\Column(name: 'created_at', type: Types::DATETIME_MUTABLE)]
    #[Groups(['user_2fa:read'])]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(name: 'updated_at', type: Types::DATETIME_MUTABLE)]
    #[Groups(['user_2fa:read'])]
    private ?\DateTimeInterface $updatedAt = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getSecretKey(): ?string
    {
        return $this->secretKey;
    }

    public function setSecretKey(string $secretKey): self
    {
        $this->secretKey = $secretKey;

        return $this;
    }

    public function getBackupCodes(): ?array
    {
        return $this->backupCodes;
    }

    public function setBackupCodes(?array $backupCodes): self
    {
        $this->backupCodes = $backupCodes;

        return $this;
    }

    public function getEnabledAt(): ?\DateTimeInterface
    {
        return $this->enabledAt;
    }

    public function setEnabledAt(?\DateTimeInterface $enabledAt): self
    {
        $this->enabledAt = $enabledAt;

        return $this;
    }

    public function getLastUsedAt(): ?\DateTimeInterface
    {
        return $this->lastUsedAt;
    }

    public function setLastUsedAt(?\DateTimeInterface $lastUsedAt): self
    {
        $this->lastUsedAt = $lastUsedAt;

        return $this;
    }

    public function getMethod(): string
    {
        return $this->method;
    }

    public function setMethod(string $method): self
    {
        $this->method = $method;

        return $this;
    }

    public function getPhoneNumber(): ?string
    {
        return $this->phoneNumber;
    }

    public function setPhoneNumber(?string $phoneNumber): self
    {
        $this->phoneNumber = $phoneNumber;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeInterface $updatedAt): self
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    #[ORM\PrePersist]
    public function onPrePersist(): void
    {
        $this->createdAt = new \DateTimeImmutable();
        $this->updatedAt = new \DateTimeImmutable();
    }

    #[ORM\PreUpdate]
    public function onPreUpdate(): void
    {
        $this->updatedAt = new \DateTimeImmutable();
    }
}