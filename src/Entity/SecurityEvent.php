<?php

namespace App\Entity;

use App\Repository\SecurityEventRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use ApiPlatform\Metadata\ApiResource;

#[ORM\Entity(repositoryClass: SecurityEventRepository::class)]
#[ORM\Table(name: 'security_events')]
#[ORM\HasLifecycleCallbacks]
#[ORM\Index(name: 'idx_security_events_event_type', columns: ['event_type'])]
#[ORM\Index(name: 'idx_security_events_category', columns: ['category'])]
#[ORM\Index(name: 'idx_security_events_severity', columns: ['severity'])]
#[ORM\Index(name: 'idx_security_events_user_id', columns: ['user_id'])]
#[ORM\Index(name: 'idx_security_events_ip_address', columns: ['ip_address'])]
#[ORM\Index(name: 'idx_security_events_timestamp', columns: ['timestamp'])]
#[ORM\Index(name: 'idx_security_events_is_resolved', columns: ['is_resolved'])]
#[ORM\Index(name: 'idx_security_events_correlation_id', columns: ['correlation_id'])]
#[ApiResource(
    normalizationContext: ['groups' => ['security_event:read']],
    security: "is_granted('ROLE_ADMIN')",
    securityMessage: 'Access denied.'
)]
class SecurityEvent
{
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'SEQUENCE')]
    #[ORM\SequenceGenerator(sequenceName: 'security_events_id_seq', allocationSize: 1, initialValue: 1)]
    #[ORM\Column(type: Types::BIGINT)]
    private ?int $id = null;

    #[ORM\Column(name: 'event_type', type: Types::STRING, length: 50)]
    #[Groups(['security_event:read'])]
    private ?string $eventType = null;

    #[ORM\Column(type: Types::STRING, length: 50)]
    #[Groups(['security_event:read'])]
    private ?string $category = null;

    #[ORM\Column(type: Types::STRING, length: 20)]
    #[Groups(['security_event:read'])]
    private string $severity = 'medium';

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(name: 'user_id', referencedColumnName: 'id', onDelete: 'SET NULL')]
    #[Groups(['security_event:read'])]
    private ?User $user = null;

    #[ORM\Column(name: 'ip_address', type: Types::STRING, length: 45, nullable: true)]
    #[Groups(['security_event:read'])]
    private ?string $ipAddress = null;

    #[ORM\Column(name: 'user_agent', type: Types::TEXT, nullable: true)]
    #[Groups(['security_event:read'])]
    private ?string $userAgent = null;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    #[Groups(['security_event:read'])]
    private ?array $location = null;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    #[Groups(['security_event:read'])]
    private ?array $details = null;

    #[ORM\Column(name: 'correlation_id', type: Types::STRING, length: 255, nullable: true)]
    #[Groups(['security_event:read'])]
    private ?string $correlationId = null;

    #[ORM\Column(type: Types::STRING, length: 20, nullable: true)]
    #[Groups(['security_event:read'])]
    private ?string $source = null;

    #[ORM\Column(name: 'is_resolved', type: Types::BOOLEAN)]
    #[Groups(['security_event:read'])]
    private bool $isResolved = false;

    #[ORM\Column(name: 'resolved_at', type: Types::DATETIME_MUTABLE, nullable: true)]
    #[Groups(['security_event:read'])]
    private ?\DateTimeInterface $resolvedAt = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(name: 'resolved_by', referencedColumnName: 'id', onDelete: 'SET NULL')]
    #[Groups(['security_event:read'])]
    private ?User $resolvedBy = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    #[Groups(['security_event:read'])]
    private ?\DateTimeInterface $timestamp = null;

    #[ORM\Column(name: 'created_at', type: Types::DATETIME_MUTABLE)]
    #[Groups(['security_event:read'])]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(name: 'updated_at', type: Types::DATETIME_MUTABLE)]
    #[Groups(['security_event:read'])]
    private ?\DateTimeInterface $updatedAt = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEventType(): ?string
    {
        return $this->eventType;
    }

    public function setEventType(string $eventType): self
    {
        $this->eventType = $eventType;

        return $this;
    }

    public function getCategory(): ?string
    {
        return $this->category;
    }

    public function setCategory(string $category): self
    {
        $this->category = $category;

        return $this;
    }

    public function getSeverity(): string
    {
        return $this->severity;
    }

    public function setSeverity(string $severity): self
    {
        $this->severity = $severity;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getIpAddress(): ?string
    {
        return $this->ipAddress;
    }

    public function setIpAddress(?string $ipAddress): self
    {
        $this->ipAddress = $ipAddress;

        return $this;
    }

    public function getUserAgent(): ?string
    {
        return $this->userAgent;
    }

    public function setUserAgent(?string $userAgent): self
    {
        $this->userAgent = $userAgent;

        return $this;
    }

    public function getLocation(): ?array
    {
        return $this->location;
    }

    public function setLocation(?array $location): self
    {
        $this->location = $location;

        return $this;
    }

    public function getDetails(): ?array
    {
        return $this->details;
    }

    public function setDetails(?array $details): self
    {
        $this->details = $details;

        return $this;
    }

    public function getCorrelationId(): ?string
    {
        return $this->correlationId;
    }

    public function setCorrelationId(?string $correlationId): self
    {
        $this->correlationId = $correlationId;

        return $this;
    }

    public function getSource(): ?string
    {
        return $this->source;
    }

    public function setSource(?string $source): self
    {
        $this->source = $source;

        return $this;
    }

    public function isResolved(): bool
    {
        return $this->isResolved;
    }

    public function setIsResolved(bool $isResolved): self
    {
        $this->isResolved = $isResolved;

        return $this;
    }

    public function getResolvedAt(): ?\DateTimeInterface
    {
        return $this->resolvedAt;
    }

    public function setResolvedAt(?\DateTimeInterface $resolvedAt): self
    {
        $this->resolvedAt = $resolvedAt;

        return $this;
    }

    public function getResolvedBy(): ?User
    {
        return $this->resolvedBy;
    }

    public function setResolvedBy(?User $resolvedBy): self
    {
        $this->resolvedBy = $resolvedBy;

        return $this;
    }

    public function getTimestamp(): ?\DateTimeInterface
    {
        return $this->timestamp;
    }

    public function setTimestamp(\DateTimeInterface $timestamp): self
    {
        $this->timestamp = $timestamp;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeInterface $updatedAt): self
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    #[ORM\PrePersist]
    public function onPrePersist(): void
    {
        $this->createdAt = new \DateTimeImmutable();
        $this->updatedAt = new \DateTimeImmutable();
        if ($this->timestamp === null) {
            $this->timestamp = new \DateTimeImmutable();
        }
    }

    #[ORM\PreUpdate]
    public function onPreUpdate(): void
    {
        $this->updatedAt = new \DateTimeImmutable();
    }
}