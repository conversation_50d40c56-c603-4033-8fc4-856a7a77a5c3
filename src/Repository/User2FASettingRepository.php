<?php

namespace App\Repository;

use App\Entity\User2FASetting;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<User2FASetting>
 *
 * @method User2FASetting|null find($id, $lockMode = null, $lockVersion = null)
 * @method User2FASetting|null findOneBy(array $criteria, array $orderBy = null)
 * @method User2FASetting[]    findAll()
 * @method User2FASetting[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class User2FASettingRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, User2FASetting::class);
    }
}