<?php

namespace App\Repository;

use App\Entity\SystemConfiguration;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<SystemConfiguration>
 *
 * @method SystemConfiguration|null find($id, $lockMode = null, $lockVersion = null)
 * @method SystemConfiguration|null findOneBy(array $criteria, array $orderBy = null)
 * @method SystemConfiguration[]    findAll()
 * @method SystemConfiguration[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SystemConfigurationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SystemConfiguration::class);
    }
}