<?php

namespace App\Repository;

use App\Entity\ConfigurationHistory;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ConfigurationHistory>
 *
 * @method ConfigurationHistory|null find($id, $lockMode = null, $lockVersion = null)
 * @method ConfigurationHistory|null findOneBy(array $criteria, array $orderBy = null)
 * @method ConfigurationHistory[]    findAll()
 * @method ConfigurationHistory[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ConfigurationHistoryRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ConfigurationHistory::class);
    }
}