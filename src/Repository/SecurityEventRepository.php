<?php

namespace App\Repository;

use App\Entity\SecurityEvent;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<SecurityEvent>
 *
 * @method SecurityEvent|null find($id, $lockMode = null, $lockVersion = null)
 * @method SecurityEvent|null findOneBy(array $criteria, array $orderBy = null)
 * @method SecurityEvent[]    findAll()
 * @method SecurityEvent[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SecurityEventRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SecurityEvent::class);
    }
}