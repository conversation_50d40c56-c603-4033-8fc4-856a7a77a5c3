<?php

namespace App\DataFixtures;

use App\Entity\ConfigurationHistory;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;

class ConfigurationHistoryFixtures extends Fixture implements DependentFixtureInterface
{
    public function getDependencies(): array
    {
        return [
            UserFixtures::class,
            SystemConfigurationFixtures::class,
        ];
    }

    public function load(ObjectManager $manager): void
    {
        // Admin user who made changes
        $adminUser = $this->getReference(UserFixtures::ADMIN_USER_REFERENCE, \App\Entity\User::class);
        
        // Configuration change for app name
        $appNameConfig = $this->getReference(SystemConfigurationFixtures::CONFIG_APP_NAME_REFERENCE, \App\Entity\SystemConfiguration::class);
        
        $appNameChange = new ConfigurationHistory();
        $appNameChange->setConfigKey($appNameConfig->getConfigKey());
        $appNameChange->setOldValue('Hospital Management System');
        $appNameChange->setNewValue($appNameConfig->getConfigValue());
        $appNameChange->setChangedBy($adminUser);
        $appNameChange->setChangeReason('Updated to reflect new branding');
        $appNameChange->setIpAddress('127.0.0.1');
        $appNameChange->setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        
        $manager->persist($appNameChange);

        // Configuration change for timezone
        $timezoneConfig = $this->getReference(SystemConfigurationFixtures::CONFIG_TIMEZONE_REFERENCE, \App\Entity\SystemConfiguration::class);
        
        $timezoneChange = new ConfigurationHistory();
        $timezoneChange->setConfigKey($timezoneConfig->getConfigKey());
        $timezoneChange->setOldValue('UTC');
        $timezoneChange->setNewValue($timezoneConfig->getConfigValue());
        $timezoneChange->setChangedBy($adminUser);
        $timezoneChange->setChangeReason('Changed to local timezone for better user experience');
        $timezoneChange->setIpAddress('127.0.0.1');
        $timezoneChange->setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        
        $manager->persist($timezoneChange);

        // Configuration change for maintenance mode
        $maintenanceConfig = $this->getReference(SystemConfigurationFixtures::CONFIG_MAINTENANCE_MODE_REFERENCE, \App\Entity\SystemConfiguration::class);
        
        $maintenanceChange = new ConfigurationHistory();
        $maintenanceChange->setConfigKey($maintenanceConfig->getConfigKey());
        $maintenanceChange->setOldValue('true');
        $maintenanceChange->setNewValue($maintenanceConfig->getConfigValue());
        $maintenanceChange->setChangedBy($adminUser);
        $maintenanceChange->setChangeReason('Disabled maintenance mode after updates');
        $maintenanceChange->setIpAddress('127.0.0.1');
        $maintenanceChange->setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        
        $manager->persist($maintenanceChange);

        // Configuration change for max login attempts
        $maxLoginAttemptsConfig = $this->getReference(SystemConfigurationFixtures::CONFIG_MAX_LOGIN_ATTEMPTS_REFERENCE, \App\Entity\SystemConfiguration::class);
        
        $maxLoginAttemptsChange = new ConfigurationHistory();
        $maxLoginAttemptsChange->setConfigKey($maxLoginAttemptsConfig->getConfigKey());
        $maxLoginAttemptsChange->setOldValue('3');
        $maxLoginAttemptsChange->setNewValue($maxLoginAttemptsConfig->getConfigValue());
        $maxLoginAttemptsChange->setChangedBy($adminUser);
        $maxLoginAttemptsChange->setChangeReason('Increased to reduce false lockouts');
        $maxLoginAttemptsChange->setIpAddress('127.0.0.1');
        $maxLoginAttemptsChange->setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        
        $manager->persist($maxLoginAttemptsChange);

        // Configuration change for session timeout
        $sessionTimeoutConfig = $this->getReference(SystemConfigurationFixtures::CONFIG_SESSION_TIMEOUT_REFERENCE, \App\Entity\SystemConfiguration::class);
        
        $sessionTimeoutChange = new ConfigurationHistory();
        $sessionTimeoutChange->setConfigKey($sessionTimeoutConfig->getConfigKey());
        $sessionTimeoutChange->setOldValue('1800');
        $sessionTimeoutChange->setNewValue($sessionTimeoutConfig->getConfigValue());
        $sessionTimeoutChange->setChangedBy($adminUser);
        $sessionTimeoutChange->setChangeReason('Extended session timeout for better user experience');
        $sessionTimeoutChange->setIpAddress('127.0.0.1');
        $sessionTimeoutChange->setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        
        $manager->persist($sessionTimeoutChange);

        // Configuration change for email host
        $emailHostChange = new ConfigurationHistory();
        $emailHostChange->setConfigKey('email.smtp.host');
        $emailHostChange->setOldValue('smtp.oldhost.com');
        $emailHostChange->setNewValue('smtp.example.com');
        $emailHostChange->setChangedBy($adminUser);
        $emailHostChange->setChangeReason('Migrated to new email provider');
        $emailHostChange->setIpAddress('127.0.0.1');
        $emailHostChange->setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        
        $manager->persist($emailHostChange);

        // Configuration change for email port
        $emailPortChange = new ConfigurationHistory();
        $emailPortChange->setConfigKey('email.smtp.port');
        $emailPortChange->setOldValue('25');
        $emailPortChange->setNewValue('587');
        $emailPortChange->setChangedBy($adminUser);
        $emailPortChange->setChangeReason('Changed to use TLS encryption');
        $emailPortChange->setIpAddress('127.0.0.1');
        $emailPortChange->setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        
        $manager->persist($emailPortChange);

        // Configuration change for currency
        $currencyChange = new ConfigurationHistory();
        $currencyChange->setConfigKey('billing.currency');
        $currencyChange->setOldValue('EUR');
        $currencyChange->setNewValue('USD');
        $currencyChange->setChangedBy($adminUser);
        $currencyChange->setChangeReason('Changed default currency to USD');
        $currencyChange->setIpAddress('127.0.0.1');
        $currencyChange->setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        
        $manager->persist($currencyChange);

        $manager->flush();
    }
}