<?php

namespace App\DataFixtures;

use App\Entity\SecurityEvent;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;

class SecurityEventFixtures extends Fixture implements DependentFixtureInterface
{
    public function getDependencies(): array
    {
        return [
            UserFixtures::class,
        ];
    }

    public function load(ObjectManager $manager): void
    {
        // Successful login event
        $adminUser = $this->getReference(UserFixtures::ADMIN_USER_REFERENCE, \App\Entity\User::class);
        
        $successfulLogin = new SecurityEvent();
        $successfulLogin->setEventType('SUCCESSFUL_LOGIN');
        $successfulLogin->setCategory('AUTHENTICATION');
        $successfulLogin->setSeverity('low');
        $successfulLogin->setUser($adminUser);
        $successfulLogin->setIpAddress('127.0.0.1');
        $successfulLogin->setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        $successfulLogin->setLocation([
            'country' => 'United States',
            'city' => 'New York',
            'latitude' => 40.7128,
            'longitude' => -74.0060
        ]);
        $successfulLogin->setDetails([
            'login_method' => 'password',
            'session_id' => 'session_admin_12345'
        ]);
        $successfulLogin->setSource('WEB');
        $successfulLogin->setIsResolved(false);
        
        $manager->persist($successfulLogin);

        // Failed login attempt event
        $nurseUser = $this->getReference(UserFixtures::NURSE_USER_REFERENCE, \App\Entity\User::class);
        
        $failedLogin = new SecurityEvent();
        $failedLogin->setEventType('FAILED_LOGIN');
        $failedLogin->setCategory('AUTHENTICATION');
        $failedLogin->setSeverity('medium');
        $failedLogin->setUser($nurseUser);
        $failedLogin->setIpAddress('127.0.0.1');
        $failedLogin->setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        $failedLogin->setLocation([
            'country' => 'United States',
            'city' => 'New York',
            'latitude' => 40.7128,
            'longitude' => -74.0060
        ]);
        $failedLogin->setDetails([
            'attempt_count' => 1,
            'failure_reason' => 'invalid_credentials'
        ]);
        $failedLogin->setSource('WEB');
        $failedLogin->setIsResolved(false);
        
        $manager->persist($failedLogin);

        // Suspicious activity event
        $patientUser = $this->getReference(UserFixtures::PATIENT_USER_REFERENCE, \App\Entity\User::class);
        
        $suspiciousActivity = new SecurityEvent();
        $suspiciousActivity->setEventType('SUSPICIOUS_ACTIVITY');
        $suspiciousActivity->setCategory('AUTHORIZATION');
        $suspiciousActivity->setSeverity('high');
        $suspiciousActivity->setUser($patientUser);
        $suspiciousActivity->setIpAddress('*************');
        $suspiciousActivity->setUserAgent('Mozilla/5.0 (Unknown; Linux x86_64) AppleWebKit/537.36');
        $suspiciousActivity->setLocation([
            'country' => 'Russia',
            'city' => 'Moscow',
            'latitude' => 55.758,
            'longitude' => 37.6173
        ]);
        $suspiciousActivity->setDetails([
            'activity_type' => 'multiple_failed_requests',
            'request_count' => 50,
            'time_window' => '5 minutes'
        ]);
        $suspiciousActivity->setSource('WEB');
        $suspiciousActivity->setIsResolved(false);
        
        $manager->persist($suspiciousActivity);

        // Brute force attempt event
        $receptionistUser = $this->getReference(UserFixtures::RECEPTIONIST_USER_REFERENCE, \App\Entity\User::class);
        
        $bruteForce = new SecurityEvent();
        $bruteForce->setEventType('BRUTE_FORCE_ATTEMPT');
        $bruteForce->setCategory('AUTHENTICATION');
        $bruteForce->setSeverity('critical');
        $bruteForce->setUser($receptionistUser);
        $bruteForce->setIpAddress('********');
        $bruteForce->setUserAgent('Mozilla/5.0 (Unknown; Linux x86_64) AppleWebKit/537.36');
        $bruteForce->setLocation([
            'country' => 'China',
            'city' => 'Beijing',
            'latitude' => 39.9042,
            'longitude' => 116.4074
        ]);
        $bruteForce->setDetails([
            'attempt_count' => 100,
            'time_window' => '10 minutes',
            'blocked' => true
        ]);
        $bruteForce->setSource('WEB');
        $bruteForce->setIsResolved(false);
        
        $manager->persist($bruteForce);

        // Unauthorized access event
        $doctorUser = $this->getReference(UserFixtures::DOCTOR_USER_REFERENCE, \App\Entity\User::class);
        
        $unauthorizedAccess = new SecurityEvent();
        $unauthorizedAccess->setEventType('UNAUTHORIZED_ACCESS');
        $unauthorizedAccess->setCategory('AUTHORIZATION');
        $unauthorizedAccess->setSeverity('high');
        $unauthorizedAccess->setUser($doctorUser);
        $unauthorizedAccess->setIpAddress('**********');
        $unauthorizedAccess->setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        $unauthorizedAccess->setLocation([
            'country' => 'United States',
            'city' => 'Los Angeles',
            'latitude' => 34.0522,
            'longitude' => -118.2437
        ]);
        $unauthorizedAccess->setDetails([
            'attempted_resource' => '/admin/users',
            'user_role' => 'ROLE_DOCTOR',
            'required_role' => 'ROLE_ADMIN'
        ]);
        $unauthorizedAccess->setSource('WEB');
        $unauthorizedAccess->setIsResolved(false);
        
        $manager->persist($unauthorizedAccess);

        // Data exfiltration event
        $adminUser = $this->getReference(UserFixtures::ADMIN_USER_REFERENCE, \App\Entity\User::class);
        
        $dataExfiltration = new SecurityEvent();
        $dataExfiltration->setEventType('DATA_EXFILTRATION');
        $dataExfiltration->setCategory('DATA_ACCESS');
        $dataExfiltration->setSeverity('critical');
        $dataExfiltration->setUser($adminUser);
        $dataExfiltration->setIpAddress('***********');
        $dataExfiltration->setUserAgent('curl/7.68.0');
        $dataExfiltration->setLocation([
            'country' => 'North Korea',
            'city' => 'Pyongyang',
            'latitude' => 39.0392,
            'longitude' => 125.7629
        ]);
        $dataExfiltration->setDetails([
            'data_type' => 'patient_records',
            'record_count' => 1000,
            'transfer_method' => 'API_export'
        ]);
        $dataExfiltration->setSource('API');
        $dataExfiltration->setIsResolved(false);
        
        $manager->persist($dataExfiltration);

        // Malware detected event
        $inactiveUser = $manager->getRepository(\App\Entity\User::class)->findOneBy(['email' => '<EMAIL>']);
        
        $malwareDetected = new SecurityEvent();
        $malwareDetected->setEventType('MALWARE_DETECTED');
        $malwareDetected->setCategory('MALWARE');
        $malwareDetected->setSeverity('critical');
        $malwareDetected->setUser($inactiveUser);
        $malwareDetected->setIpAddress('************');
        $malwareDetected->setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        $malwareDetected->setLocation([
            'country' => 'United States',
            'city' => 'Chicago',
            'latitude' => 41.8781,
            'longitude' => -87.6298
        ]);
        $malwareDetected->setDetails([
            'malware_type' => 'trojan',
            'file_name' => 'malicious_attachment.exe',
            'detection_method' => 'antivirus_scan'
        ]);
        $malwareDetected->setSource('WEB');
        $malwareDetected->setIsResolved(false);
        
        $manager->persist($malwareDetected);

        // Resolved security event
        $pendingUser = $manager->getRepository(\App\Entity\User::class)->findOneBy(['email' => '<EMAIL>']);
        
        $resolvedEvent = new SecurityEvent();
        $resolvedEvent->setEventType('FAILED_LOGIN');
        $resolvedEvent->setCategory('AUTHENTICATION');
        $resolvedEvent->setSeverity('medium');
        $resolvedEvent->setUser($pendingUser);
        $resolvedEvent->setIpAddress('127.0.0.1');
        $resolvedEvent->setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        $resolvedEvent->setLocation([
            'country' => 'United States',
            'city' => 'New York',
            'latitude' => 40.7128,
            'longitude' => -74.0060
        ]);
        $resolvedEvent->setDetails([
            'attempt_count' => 3,
            'failure_reason' => 'invalid_credentials'
        ]);
        $resolvedEvent->setSource('WEB');
        $resolvedEvent->setIsResolved(true);
        $resolvedEvent->setResolvedAt(new \DateTime());
        $resolvedEvent->setResolvedBy($adminUser);
        
        $manager->persist($resolvedEvent);

        $manager->flush();
    }
}