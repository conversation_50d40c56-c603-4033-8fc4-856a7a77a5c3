<?php

namespace App\DataFixtures;

use App\Entity\RolePermission;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;

class RolePermissionFixtures extends Fixture implements DependentFixtureInterface
{
    public function getDependencies(): array
    {
        return [
            RoleFixtures::class,
            PermissionFixtures::class,
        ];
    }

    public function load(ObjectManager $manager): void
    {
        $adminUser = $this->getReference(UserFixtures::ADMIN_USER_REFERENCE, \App\Entity\User::class);
        
        // Admin role permissions (all permissions)
        $adminRole = $this->getReference(RoleFixtures::ROLE_ADMIN_REFERENCE, \App\Entity\Role::class);
        
        // Get all permissions
        $permissions = [
            PermissionFixtures::PERMISSION_USER_CREATE_REFERENCE,
            PermissionFixtures::PERMISSION_USER_READ_REFERENCE,
            PermissionFixtures::PERMISSION_USER_UPDATE_REFERENCE,
            PermissionFixtures::PERMISSION_USER_DELETE_REFERENCE,
            PermissionFixtures::PERMISSION_PATIENT_CREATE_REFERENCE,
            PermissionFixtures::PERMISSION_PATIENT_READ_REFERENCE,
            PermissionFixtures::PERMISSION_PATIENT_UPDATE_REFERENCE,
            PermissionFixtures::PERMISSION_PATIENT_DELETE_REFERENCE,
            PermissionFixtures::PERMISSION_DOCTOR_CREATE_REFERENCE,
            PermissionFixtures::PERMISSION_DOCTOR_READ_REFERENCE,
            PermissionFixtures::PERMISSION_DOCTOR_UPDATE_REFERENCE,
            PermissionFixtures::PERMISSION_DOCTOR_DELETE_REFERENCE,
            PermissionFixtures::PERMISSION_APPOINTMENT_CREATE_REFERENCE,
            PermissionFixtures::PERMISSION_APPOINTMENT_READ_REFERENCE,
            PermissionFixtures::PERMISSION_APPOINTMENT_UPDATE_REFERENCE,
            PermissionFixtures::PERMISSION_APPOINTMENT_DELETE_REFERENCE,
            PermissionFixtures::PERMISSION_MEDICAL_RECORD_CREATE_REFERENCE,
            PermissionFixtures::PERMISSION_MEDICAL_RECORD_READ_REFERENCE,
            PermissionFixtures::PERMISSION_MEDICAL_RECORD_UPDATE_REFERENCE,
            PermissionFixtures::PERMISSION_MEDICAL_RECORD_DELETE_REFERENCE,
            PermissionFixtures::PERMISSION_TEST_RESULT_CREATE_REFERENCE,
            PermissionFixtures::PERMISSION_TEST_RESULT_READ_REFERENCE,
            PermissionFixtures::PERMISSION_TEST_RESULT_UPDATE_REFERENCE,
            PermissionFixtures::PERMISSION_TEST_RESULT_DELETE_REFERENCE,
            PermissionFixtures::PERMISSION_PRESCRIPTION_CREATE_REFERENCE,
            PermissionFixtures::PERMISSION_PRESCRIPTION_READ_REFERENCE,
            PermissionFixtures::PERMISSION_PRESCRIPTION_UPDATE_REFERENCE,
            PermissionFixtures::PERMISSION_PRESCRIPTION_DELETE_REFERENCE,
            PermissionFixtures::PERMISSION_BILLING_CREATE_REFERENCE,
            PermissionFixtures::PERMISSION_BILLING_READ_REFERENCE,
            PermissionFixtures::PERMISSION_BILLING_UPDATE_REFERENCE,
            PermissionFixtures::PERMISSION_BILLING_DELETE_REFERENCE,
        ];
        
        foreach ($permissions as $permissionRef) {
            $permission = $this->getReference($permissionRef, \App\Entity\Permission::class);
            
            $rolePermission = new RolePermission();
            $rolePermission->setRole($adminRole);
            $rolePermission->setPermission($permission);
            $rolePermission->setGrantedBy($adminUser);
            $rolePermission->setGrantedAt(new \DateTimeImmutable());
            
            $manager->persist($rolePermission);
        }

        // Doctor role permissions
        $doctorRole = $this->getReference(RoleFixtures::ROLE_DOCTOR_REFERENCE, \App\Entity\Role::class);
        
        $doctorPermissions = [
            PermissionFixtures::PERMISSION_PATIENT_READ_REFERENCE,
            PermissionFixtures::PERMISSION_PATIENT_UPDATE_REFERENCE,
            PermissionFixtures::PERMISSION_MEDICAL_RECORD_CREATE_REFERENCE,
            PermissionFixtures::PERMISSION_MEDICAL_RECORD_READ_REFERENCE,
            PermissionFixtures::PERMISSION_MEDICAL_RECORD_UPDATE_REFERENCE,
            PermissionFixtures::PERMISSION_TEST_RESULT_CREATE_REFERENCE,
            PermissionFixtures::PERMISSION_TEST_RESULT_READ_REFERENCE,
            PermissionFixtures::PERMISSION_TEST_RESULT_UPDATE_REFERENCE,
            PermissionFixtures::PERMISSION_PRESCRIPTION_CREATE_REFERENCE,
            PermissionFixtures::PERMISSION_PRESCRIPTION_READ_REFERENCE,
            PermissionFixtures::PERMISSION_PRESCRIPTION_UPDATE_REFERENCE,
        ];
        
        foreach ($doctorPermissions as $permissionRef) {
            $permission = $this->getReference($permissionRef, \App\Entity\Permission::class);
            
            $rolePermission = new RolePermission();
            $rolePermission->setRole($doctorRole);
            $rolePermission->setPermission($permission);
            $rolePermission->setGrantedBy($adminUser);
            $rolePermission->setGrantedAt(new \DateTimeImmutable());
            
            $manager->persist($rolePermission);
        }

        // Patient role permissions
        $patientRole = $this->getReference(RoleFixtures::ROLE_PATIENT_REFERENCE, \App\Entity\Role::class);
        
        $patientPermissions = [
            PermissionFixtures::PERMISSION_PATIENT_READ_REFERENCE,
            PermissionFixtures::PERMISSION_APPOINTMENT_CREATE_REFERENCE,
            PermissionFixtures::PERMISSION_APPOINTMENT_READ_REFERENCE,
            PermissionFixtures::PERMISSION_MEDICAL_RECORD_READ_REFERENCE,
            PermissionFixtures::PERMISSION_TEST_RESULT_READ_REFERENCE,
            PermissionFixtures::PERMISSION_PRESCRIPTION_READ_REFERENCE,
        ];
        
        foreach ($patientPermissions as $permissionRef) {
            $permission = $this->getReference($permissionRef, \App\Entity\Permission::class);
            
            $rolePermission = new RolePermission();
            $rolePermission->setRole($patientRole);
            $rolePermission->setPermission($permission);
            $rolePermission->setGrantedBy($adminUser);
            $rolePermission->setGrantedAt(new \DateTimeImmutable());
            
            $manager->persist($rolePermission);
        }

        // Nurse role permissions
        $nurseRole = $this->getReference(RoleFixtures::ROLE_NURSE_REFERENCE, \App\Entity\Role::class);
        
        $nursePermissions = [
            PermissionFixtures::PERMISSION_PATIENT_READ_REFERENCE,
            PermissionFixtures::PERMISSION_PATIENT_UPDATE_REFERENCE,
            PermissionFixtures::PERMISSION_MEDICAL_RECORD_READ_REFERENCE,
            PermissionFixtures::PERMISSION_MEDICAL_RECORD_UPDATE_REFERENCE,
            PermissionFixtures::PERMISSION_TEST_RESULT_READ_REFERENCE,
            PermissionFixtures::PERMISSION_TEST_RESULT_UPDATE_REFERENCE,
        ];
        
        foreach ($nursePermissions as $permissionRef) {
            $permission = $this->getReference($permissionRef, \App\Entity\Permission::class);
            
            $rolePermission = new RolePermission();
            $rolePermission->setRole($nurseRole);
            $rolePermission->setPermission($permission);
            $rolePermission->setGrantedBy($adminUser);
            $rolePermission->setGrantedAt(new \DateTimeImmutable());
            
            $manager->persist($rolePermission);
        }

        // Receptionist role permissions
        $receptionistRole = $this->getReference(RoleFixtures::ROLE_RECEPTIONIST_REFERENCE, \App\Entity\Role::class);
        
        $receptionistPermissions = [
            PermissionFixtures::PERMISSION_PATIENT_CREATE_REFERENCE,
            PermissionFixtures::PERMISSION_PATIENT_READ_REFERENCE,
            PermissionFixtures::PERMISSION_PATIENT_UPDATE_REFERENCE,
            PermissionFixtures::PERMISSION_APPOINTMENT_CREATE_REFERENCE,
            PermissionFixtures::PERMISSION_APPOINTMENT_READ_REFERENCE,
            PermissionFixtures::PERMISSION_APPOINTMENT_UPDATE_REFERENCE,
            PermissionFixtures::PERMISSION_APPOINTMENT_DELETE_REFERENCE,
        ];
        
        foreach ($receptionistPermissions as $permissionRef) {
            $permission = $this->getReference($permissionRef, \App\Entity\Permission::class);
            
            $rolePermission = new RolePermission();
            $rolePermission->setRole($receptionistRole);
            $rolePermission->setPermission($permission);
            $rolePermission->setGrantedBy($adminUser);
            $rolePermission->setGrantedAt(new \DateTimeImmutable());
            
            $manager->persist($rolePermission);
        }

        // Lab technician role permissions
        $labTechnicianRole = $this->getReference(RoleFixtures::ROLE_LAB_TECHNICIAN_REFERENCE, \App\Entity\Role::class);
        
        $labTechnicianPermissions = [
            PermissionFixtures::PERMISSION_PATIENT_READ_REFERENCE,
            PermissionFixtures::PERMISSION_TEST_RESULT_CREATE_REFERENCE,
            PermissionFixtures::PERMISSION_TEST_RESULT_READ_REFERENCE,
            PermissionFixtures::PERMISSION_TEST_RESULT_UPDATE_REFERENCE,
            PermissionFixtures::PERMISSION_TEST_RESULT_DELETE_REFERENCE,
        ];
        
        foreach ($labTechnicianPermissions as $permissionRef) {
            $permission = $this->getReference($permissionRef, \App\Entity\Permission::class);
            
            $rolePermission = new RolePermission();
            $rolePermission->setRole($labTechnicianRole);
            $rolePermission->setPermission($permission);
            $rolePermission->setGrantedBy($adminUser);
            $rolePermission->setGrantedAt(new \DateTimeImmutable());
            
            $manager->persist($rolePermission);
        }

        // Pharmacist role permissions
        $pharmacistRole = $this->getReference(RoleFixtures::ROLE_PHARMACIST_REFERENCE, \App\Entity\Role::class);
        
        $pharmacistPermissions = [
            PermissionFixtures::PERMISSION_PATIENT_READ_REFERENCE,
            PermissionFixtures::PERMISSION_PRESCRIPTION_READ_REFERENCE,
            PermissionFixtures::PERMISSION_PRESCRIPTION_UPDATE_REFERENCE,
        ];
        
        foreach ($pharmacistPermissions as $permissionRef) {
            $permission = $this->getReference($permissionRef, \App\Entity\Permission::class);
            
            $rolePermission = new RolePermission();
            $rolePermission->setRole($pharmacistRole);
            $rolePermission->setPermission($permission);
            $rolePermission->setGrantedBy($adminUser);
            $rolePermission->setGrantedAt(new \DateTimeImmutable());
            
            $manager->persist($rolePermission);
        }

        $manager->flush();
    }
}