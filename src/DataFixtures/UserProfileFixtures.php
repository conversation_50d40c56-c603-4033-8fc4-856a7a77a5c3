<?php

namespace App\DataFixtures;

use App\Entity\User;
use App\Entity\UserProfile;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;

class UserProfileFixtures extends Fixture implements DependentFixtureInterface
{
    public const ADMIN_PROFILE_REFERENCE = 'admin-profile';
    public const DOCTOR_PROFILE_REFERENCE = 'doctor-profile';
    public const PATIENT_PROFILE_REFERENCE = 'patient-profile';
    public const NURSE_PROFILE_REFERENCE = 'nurse-profile';
    public const RECEPTIONIST_PROFILE_REFERENCE = 'receptionist-profile';

    public function getDependencies(): array
    {
        return [
            UserFixtures::class,
        ];
    }

    public function load(ObjectManager $manager): void
    {
        // Create admin profile
        $adminProfile = new UserProfile();
        $adminProfile->setFirstName('Admin');
        $adminProfile->setLastName('User');
        
        // Link to admin user
        $adminUser = $this->getReference(UserFixtures::ADMIN_USER_REFERENCE, User::class);
        $adminProfile->setUser($adminUser);
        $adminUser->setProfile($adminProfile);
        
        $manager->persist($adminProfile);
        $this->addReference(self::ADMIN_PROFILE_REFERENCE, $adminProfile);

        // Create doctor profile
        $doctorProfile = new UserProfile();
        $doctorProfile->setFirstName('John');
        $doctorProfile->setLastName('Doctor');
        
        // Link to doctor user
        $doctorUser = $this->getReference(UserFixtures::DOCTOR_USER_REFERENCE, User::class);
        $doctorProfile->setUser($doctorUser);
        $doctorUser->setProfile($doctorProfile);
        
        $manager->persist($doctorProfile);
        $this->addReference(self::DOCTOR_PROFILE_REFERENCE, $doctorProfile);

        // Create patient profile
        $patientProfile = new UserProfile();
        $patientProfile->setFirstName('Jane');
        $patientProfile->setLastName('Patient');
        
        // Link to patient user
        $patientUser = $this->getReference(UserFixtures::PATIENT_USER_REFERENCE, User::class);
        $patientProfile->setUser($patientUser);
        $patientUser->setProfile($patientProfile);
        
        $manager->persist($patientProfile);
        $this->addReference(self::PATIENT_PROFILE_REFERENCE, $patientProfile);

        // Create nurse profile
        $nurseProfile = new UserProfile();
        $nurseProfile->setFirstName('Nurse');
        $nurseProfile->setLastName('Joy');
        
        // Link to nurse user
        $nurseUser = $this->getReference(UserFixtures::NURSE_USER_REFERENCE, User::class);
        $nurseProfile->setUser($nurseUser);
        $nurseUser->setProfile($nurseProfile);
        
        $manager->persist($nurseProfile);
        $this->addReference(self::NURSE_PROFILE_REFERENCE, $nurseProfile);

        // Create receptionist profile
        $receptionistProfile = new UserProfile();
        $receptionistProfile->setFirstName('Reception');
        $receptionistProfile->setLastName('Rachel');
        
        // Link to receptionist user
        $receptionistUser = $this->getReference(UserFixtures::RECEPTIONIST_USER_REFERENCE, User::class);
        $receptionistProfile->setUser($receptionistUser);
        $receptionistUser->setProfile($receptionistProfile);
        
        $manager->persist($receptionistProfile);
        $this->addReference(self::RECEPTIONIST_PROFILE_REFERENCE, $receptionistProfile);

        // Create some additional profiles without users
        $orphanProfile1 = new UserProfile();
        $orphanProfile1->setFirstName('Orphan');
        $orphanProfile1->setLastName('One');
        $manager->persist($orphanProfile1);

        $orphanProfile2 = new UserProfile();
        $orphanProfile2->setFirstName('Orphan');
        $orphanProfile2->setLastName('Two');
        $manager->persist($orphanProfile2);

        $manager->flush();
    }
}