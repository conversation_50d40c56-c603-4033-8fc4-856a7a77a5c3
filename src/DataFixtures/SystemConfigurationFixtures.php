<?php

namespace App\DataFixtures;

use App\Entity\SystemConfiguration;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

class SystemConfigurationFixtures extends Fixture
{
    public const CONFIG_APP_NAME_REFERENCE = 'config-app-name';
    public const CONFIG_TIMEZONE_REFERENCE = 'config-timezone';
    public const CONFIG_MAINTENANCE_MODE_REFERENCE = 'config-maintenance-mode';
    public const CONFIG_MAX_LOGIN_ATTEMPTS_REFERENCE = 'config-max-login-attempts';
    public const CONFIG_SESSION_TIMEOUT_REFERENCE = 'config-session-timeout';

    public function load(ObjectManager $manager): void
    {
        // Application name configuration
        $appName = new SystemConfiguration();
        $appName->setConfigKey('app.name');
        $appName->setConfigValue('Healthcare Management System');
        $appName->setConfigType('string');
        $appName->setCategory('system');
        $appName->setDescription('Application name displayed in the UI');
        $appName->setIsSystemSetting(true);
        $appName->setIsEditable(true);
        $appName->setDefaultValue('Healthcare Management System');
        
        $manager->persist($appName);
        $this->addReference(self::CONFIG_APP_NAME_REFERENCE, $appName);

        // Timezone configuration
        $timezone = new SystemConfiguration();
        $timezone->setConfigKey('app.timezone');
        $timezone->setConfigValue('America/New_York');
        $timezone->setConfigType('string');
        $timezone->setCategory('system');
        $timezone->setDescription('Default timezone for the application');
        $timezone->setIsSystemSetting(true);
        $timezone->setIsEditable(true);
        $timezone->setDefaultValue('UTC');
        $timezone->setValidationRules([
            'type' => 'string',
            'pattern' => '/^[A-Za-z\/_]+$/'
        ]);
        
        $manager->persist($timezone);
        $this->addReference(self::CONFIG_TIMEZONE_REFERENCE, $timezone);

        // Maintenance mode configuration
        $maintenanceMode = new SystemConfiguration();
        $maintenanceMode->setConfigKey('app.maintenance_mode');
        $maintenanceMode->setConfigValue('false');
        $maintenanceMode->setConfigType('boolean');
        $maintenanceMode->setCategory('system');
        $maintenanceMode->setDescription('Enable or disable maintenance mode');
        $maintenanceMode->setIsSystemSetting(true);
        $maintenanceMode->setIsEditable(true);
        $maintenanceMode->setDefaultValue('false');
        $maintenanceMode->setValidationRules([
            'type' => 'boolean'
        ]);
        
        $manager->persist($maintenanceMode);
        $this->addReference(self::CONFIG_MAINTENANCE_MODE_REFERENCE, $maintenanceMode);

        // Maximum login attempts configuration
        $maxLoginAttempts = new SystemConfiguration();
        $maxLoginAttempts->setConfigKey('security.max_login_attempts');
        $maxLoginAttempts->setConfigValue('5');
        $maxLoginAttempts->setConfigType('integer');
        $maxLoginAttempts->setCategory('security');
        $maxLoginAttempts->setDescription('Maximum number of failed login attempts before account lockout');
        $maxLoginAttempts->setIsSystemSetting(true);
        $maxLoginAttempts->setIsEditable(true);
        $maxLoginAttempts->setDefaultValue('3');
        $maxLoginAttempts->setValidationRules([
            'type' => 'integer',
            'minimum' => 1,
            'maximum' => 10
        ]);
        
        $manager->persist($maxLoginAttempts);
        $this->addReference(self::CONFIG_MAX_LOGIN_ATTEMPTS_REFERENCE, $maxLoginAttempts);

        // Session timeout configuration
        $sessionTimeout = new SystemConfiguration();
        $sessionTimeout->setConfigKey('security.session_timeout');
        $sessionTimeout->setConfigValue('3600');
        $sessionTimeout->setConfigType('integer');
        $sessionTimeout->setCategory('security');
        $sessionTimeout->setDescription('Session timeout in seconds');
        $sessionTimeout->setIsSystemSetting(true);
        $sessionTimeout->setIsEditable(true);
        $sessionTimeout->setDefaultValue('1800');
        $sessionTimeout->setValidationRules([
            'type' => 'integer',
            'minimum' => 300,
            'maximum' => 86400
        ]);
        
        $manager->persist($sessionTimeout);
        $this->addReference(self::CONFIG_SESSION_TIMEOUT_REFERENCE, $sessionTimeout);

        // Email configuration
        $emailHost = new SystemConfiguration();
        $emailHost->setConfigKey('email.smtp.host');
        $emailHost->setConfigValue('smtp.example.com');
        $emailHost->setConfigType('string');
        $emailHost->setCategory('email');
        $emailHost->setDescription('SMTP server host for sending emails');
        $emailHost->setIsSystemSetting(false);
        $emailHost->setIsEditable(true);
        $emailHost->setDefaultValue('localhost');
        
        $manager->persist($emailHost);

        $emailPort = new SystemConfiguration();
        $emailPort->setConfigKey('email.smtp.port');
        $emailPort->setConfigValue('587');
        $emailPort->setConfigType('integer');
        $emailPort->setCategory('email');
        $emailPort->setDescription('SMTP server port for sending emails');
        $emailPort->setIsSystemSetting(false);
        $emailPort->setIsEditable(true);
        $emailPort->setDefaultValue('25');
        $emailPort->setValidationRules([
            'type' => 'integer',
            'minimum' => 1,
            'maximum' => 65535
        ]);
        
        $manager->persist($emailPort);

        $emailUsername = new SystemConfiguration();
        $emailUsername->setConfigKey('email.smtp.username');
        $emailUsername->setConfigValue('<EMAIL>');
        $emailUsername->setConfigType('string');
        $emailUsername->setCategory('email');
        $emailUsername->setDescription('SMTP username for sending emails');
        $emailUsername->setIsSystemSetting(false);
        $emailUsername->setIsEditable(true);
        $emailUsername->setDefaultValue('');
        
        $manager->persist($emailUsername);

        $emailPassword = new SystemConfiguration();
        $emailPassword->setConfigKey('email.smtp.password');
        $emailPassword->setConfigValue('encrypted_password_here');
        $emailPassword->setConfigType('string');
        $emailPassword->setCategory('email');
        $emailPassword->setDescription('SMTP password for sending emails (encrypted)');
        $emailPassword->setIsSystemSetting(false);
        $emailPassword->setIsEditable(true);
        $emailPassword->setDefaultValue('');
        
        $manager->persist($emailPassword);

        $emailEncryption = new SystemConfiguration();
        $emailEncryption->setConfigKey('email.smtp.encryption');
        $emailEncryption->setConfigValue('tls');
        $emailEncryption->setConfigType('string');
        $emailEncryption->setCategory('email');
        $emailEncryption->setDescription('SMTP encryption method');
        $emailEncryption->setIsSystemSetting(false);
        $emailEncryption->setIsEditable(true);
        $emailEncryption->setDefaultValue('none');
        $emailEncryption->setValidationRules([
            'type' => 'string',
            'enum' => ['none', 'tls', 'ssl']
        ]);
        
        $manager->persist($emailEncryption);

        // Billing configuration
        $currency = new SystemConfiguration();
        $currency->setConfigKey('billing.currency');
        $currency->setConfigValue('USD');
        $currency->setConfigType('string');
        $currency->setCategory('billing');
        $currency->setDescription('Default currency for billing');
        $currency->setIsSystemSetting(false);
        $currency->setIsEditable(true);
        $currency->setDefaultValue('USD');
        $currency->setValidationRules([
            'type' => 'string',
            'pattern' => '/^[A-Z]{3}$/'
        ]);
        
        $manager->persist($currency);

        $taxRate = new SystemConfiguration();
        $taxRate->setConfigKey('billing.tax_rate');
        $taxRate->setConfigValue('0.07');
        $taxRate->setConfigType('string');
        $taxRate->setCategory('billing');
        $taxRate->setDescription('Default tax rate for billing');
        $taxRate->setIsSystemSetting(false);
        $taxRate->setIsEditable(true);
        $taxRate->setDefaultValue('0.00');
        $taxRate->setValidationRules([
            'type' => 'number',
            'minimum' => 0,
            'maximum' => 1
        ]);
        
        $manager->persist($taxRate);

        $manager->flush();
    }
}