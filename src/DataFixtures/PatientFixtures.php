<?php

namespace App\DataFixtures;

use App\Entity\Patient;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;

class PatientFixtures extends Fixture implements DependentFixtureInterface
{
    public const PATIENT_JANE_REFERENCE = 'patient-jane';
    public const PATIENT_JOHN_REFERENCE = 'patient-john';
    public const PATIENT_BOB_REFERENCE = 'patient-bob';
    public const PATIENT_ALICE_REFERENCE = 'patient-alice';
    public const PATIENT_CHARLIE_REFERENCE = 'patient-charlie';

    public function getDependencies(): array
    {
        return [
            UserFixtures::class,
        ];
    }

    public function load(ObjectManager $manager): void
    {
        // Create patient Jane
        $jane = new Patient();
        $jane->setPatientId('********');
        $jane->setFirstName('Jane');
        $jane->setLastName('Doe');
        $jane->setDateOfBirth(new \DateTime('1990-05-15'));
        $jane->setGender('female');
        $jane->setPhone('+**********');
        $jane->setEmail('<EMAIL>');
        $jane->setAddress([
            'street' => '123 Main St',
            'city' => 'New York',
            'state' => 'NY',
            'country' => 'USA'
        ]);
        $jane->setDistrict('Manhattan');
        $jane->setVillage('Midtown');
        $jane->setPostalCode('10001');
        $jane->setEmergencyContactName('John Doe');
        $jane->setEmergencyContactPhone('+**********');
        $jane->setBloodType('O+');
        $jane->setHeight('165.00');
        $jane->setWeight('60.00');
        
        // Link to patient user
        $patientUser = $this->getReference(UserFixtures::PATIENT_USER_REFERENCE, \App\Entity\User::class);
        $jane->setUser($patientUser);
        $patientUser->setPatient($jane);
        
        $manager->persist($jane);
        $this->addReference(self::PATIENT_JANE_REFERENCE, $jane);

        // Create patient John
        $john = new Patient();
        $john->setPatientId('P2501002');
        $john->setFirstName('John');
        $john->setLastName('Smith');
        $john->setDateOfBirth(new \DateTime('1985-12-03'));
        $john->setGender('male');
        $john->setPhone('+**********');
        $john->setEmail('<EMAIL>');
        $john->setAddress([
            'street' => '456 Oak Ave',
            'city' => 'Los Angeles',
            'state' => 'CA',
            'country' => 'USA'
        ]);
        $john->setDistrict('Hollywood');
        $john->setVillage('West Hollywood');
        $john->setPostalCode('90210');
        $john->setEmergencyContactName('Jane Smith');
        $john->setEmergencyContactPhone('+**********');
        $john->setBloodType('A-');
        $john->setHeight('180.00');
        $john->setWeight('80.00');
        
        $manager->persist($john);
        $this->addReference(self::PATIENT_JOHN_REFERENCE, $john);

        // Create patient Bob
        $bob = new Patient();
        $bob->setPatientId('P2501003');
        $bob->setFirstName('Bob');
        $bob->setLastName('Johnson');
        $bob->setDateOfBirth(new \DateTime('1978-08-22'));
        $bob->setGender('male');
        $bob->setPhone('+**********');
        $bob->setEmail('<EMAIL>');
        $bob->setAddress([
            'street' => '789 Pine St',
            'city' => 'Chicago',
            'state' => 'IL',
            'country' => 'USA'
        ]);
        $bob->setDistrict('Downtown');
        $bob->setVillage('The Loop');
        $bob->setPostalCode('60601');
        $bob->setEmergencyContactName('Alice Johnson');
        $bob->setEmergencyContactPhone('+**********');
        $bob->setBloodType('B+');
        $bob->setHeight('175.00');
        $bob->setWeight('75.00');
        
        $manager->persist($bob);
        $this->addReference(self::PATIENT_BOB_REFERENCE, $bob);

        // Create patient Alice
        $alice = new Patient();
        $alice->setPatientId('P2501004');
        $alice->setFirstName('Alice');
        $alice->setLastName('Williams');
        $alice->setDateOfBirth(new \DateTime('1995-03-10'));
        $alice->setGender('female');
        $alice->setPhone('+**********');
        $alice->setEmail('<EMAIL>');
        $alice->setAddress([
            'street' => '321 Elm St',
            'city' => 'Houston',
            'state' => 'TX',
            'country' => 'USA'
        ]);
        $alice->setDistrict('Medical Center');
        $alice->setVillage('Midtown');
        $alice->setPostalCode('77001');
        $alice->setEmergencyContactName('Charlie Williams');
        $alice->setEmergencyContactPhone('+**********');
        $alice->setBloodType('AB+');
        $alice->setHeight('170.00');
        $alice->setWeight('65.00');
        
        $manager->persist($alice);
        $this->addReference(self::PATIENT_ALICE_REFERENCE, $alice);

        // Create patient Charlie
        $charlie = new Patient();
        $charlie->setPatientId('P2501005');
        $charlie->setFirstName('Charlie');
        $charlie->setLastName('Brown');
        $charlie->setDateOfBirth(new \DateTime('1982-11-28'));
        $charlie->setGender('male');
        $charlie->setPhone('+**********');
        $charlie->setEmail('<EMAIL>');
        $charlie->setAddress([
            'street' => '654 Maple Ave',
            'city' => 'Phoenix',
            'state' => 'AZ',
            'country' => 'USA'
        ]);
        $charlie->setDistrict('Downtown');
        $charlie->setVillage('Central City');
        $charlie->setPostalCode('85001');
        $charlie->setEmergencyContactName('Diana Brown');
        $charlie->setEmergencyContactPhone('+**********');
        $charlie->setBloodType('O-');
        $charlie->setHeight('185.00');
        $charlie->setWeight('85.00');
        
        $manager->persist($charlie);
        $this->addReference(self::PATIENT_CHARLIE_REFERENCE, $charlie);

        // Create a patient without a user account
        $orphan = new Patient();
        $orphan->setPatientId('P2501006');
        $orphan->setFirstName('Orphan');
        $orphan->setLastName('Patient');
        $orphan->setDateOfBirth(new \DateTime('1992-07-14'));
        $orphan->setGender('other');
        $orphan->setPhone('+**********');
        $orphan->setEmail('<EMAIL>');
        $orphan->setAddress([
            'street' => '987 Cedar St',
            'city' => 'Philadelphia',
            'state' => 'PA',
            'country' => 'USA'
        ]);
        $orphan->setDistrict('Center City');
        $orphan->setVillage('University City');
        $orphan->setPostalCode('19101');
        $orphan->setEmergencyContactName('Eve Patient');
        $orphan->setEmergencyContactPhone('+**********');
        $orphan->setBloodType('A+');
        $orphan->setHeight('172.00');
        $orphan->setWeight('70.00');
        
        $manager->persist($orphan);

        $manager->flush();
    }
}