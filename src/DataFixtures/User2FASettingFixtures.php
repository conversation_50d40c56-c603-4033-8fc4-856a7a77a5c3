<?php

namespace App\DataFixtures;

use App\Entity\User2FASetting;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;

class User2FASettingFixtures extends Fixture implements DependentFixtureInterface
{
    public const TFA_ADMIN_REFERENCE = 'tfa-admin';
    public const TFA_DOCTOR_REFERENCE = 'tfa-doctor';

    public function getDependencies(): array
    {
        return [
            UserFixtures::class,
        ];
    }

    public function load(ObjectManager $manager): void
    {
        // Admin user 2FA settings
        $adminUser = $this->getReference(UserFixtures::ADMIN_USER_REFERENCE, \App\Entity\User::class);
        
        $admin2FA = new User2FASetting();
        $admin2FA->setUser($adminUser);
        $admin2FA->setSecretKey('ADMIN_SECRET_KEY_1234567890');
        $admin2FA->setBackupCodes(['code1', 'code2', 'code3', 'code4', 'code5']);
        $admin2FA->setEnabledAt(new \DateTime());
        $admin2FA->setLastUsedAt(new \DateTime('-1 day'));
        $admin2FA->setMethod('totp');
        $admin2FA->setPhoneNumber('+1234567890');
        
        $manager->persist($admin2FA);
        $this->addReference(self::TFA_ADMIN_REFERENCE, $admin2FA);

        // Doctor user 2FA settings
        $doctorUser = $this->getReference(UserFixtures::DOCTOR_USER_REFERENCE, \App\Entity\User::class);
        
        $doctor2FA = new User2FASetting();
        $doctor2FA->setUser($doctorUser);
        $doctor2FA->setSecretKey('DOCTOR_SECRET_KEY_0987654321');
        $doctor2FA->setBackupCodes(['doc1', 'doc2', 'doc3', 'doc4', 'doc5']);
        $doctor2FA->setEnabledAt(new \DateTime());
        $doctor2FA->setLastUsedAt(new \DateTime('-2 hours'));
        $doctor2FA->setMethod('sms');
        $doctor2FA->setPhoneNumber('+**********');
        
        $manager->persist($doctor2FA);
        $this->addReference(self::TFA_DOCTOR_REFERENCE, $doctor2FA);

        // Patient user with 2FA disabled (no enabledAt)
        $patientUser = $this->getReference(UserFixtures::PATIENT_USER_REFERENCE, \App\Entity\User::class);
        
        $patient2FA = new User2FASetting();
        $patient2FA->setUser($patientUser);
        $patient2FA->setSecretKey('PATIENT_SECRET_KEY_122334455');
        $patient2FA->setBackupCodes(['pat1', 'pat2', 'pat3', 'pat4', 'pat5']);
        // Not enabled - enabledAt is null
        // Not used - lastUsedAt is null
        $patient2FA->setMethod('email');
        $patient2FA->setPhoneNumber('+**********');
        
        $manager->persist($patient2FA);

        $manager->flush();
    }
}