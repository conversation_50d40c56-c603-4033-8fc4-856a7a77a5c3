<?php

namespace App\DataFixtures;

use App\Entity\UserVerification;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;

class UserVerificationFixtures extends Fixture implements DependentFixtureInterface
{
    public const VERIFICATION_ADMIN_REFERENCE = 'verification-admin';
    public const VERIFICATION_DOCTOR_REFERENCE = 'verification-doctor';
    public const VERIFICATION_PATIENT_REFERENCE = 'verification-patient';

    public function getDependencies(): array
    {
        return [
            UserFixtures::class,
        ];
    }

    public function load(ObjectManager $manager): void
    {
        // Admin user verification
        $adminUser = $this->getReference(UserFixtures::ADMIN_USER_REFERENCE, \App\Entity\User::class);
        
        $adminVerification = new UserVerification();
        $adminVerification->setUser($adminUser);
        $adminVerification->setVerificationType('email_verification');
        $adminVerification->setVerificationMethod('email');
        $adminVerification->setToken('admin_verification_token_12345');
        $adminVerification->setExpiresAt(new \DateTime('+1 day'));
        $adminVerification->setVerifiedAt(new \DateTime());
        $adminVerification->setIpAddress('127.0.0.1');
        $adminVerification->setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        
        $manager->persist($adminVerification);
        $this->addReference(self::VERIFICATION_ADMIN_REFERENCE, $adminVerification);

        // Doctor user verification
        $doctorUser = $this->getReference(UserFixtures::DOCTOR_USER_REFERENCE, \App\Entity\User::class);
        
        $doctorVerification = new UserVerification();
        $doctorVerification->setUser($doctorUser);
        $doctorVerification->setVerificationType('email_verification');
        $doctorVerification->setVerificationMethod('email');
        $doctorVerification->setToken('doctor_verification_token_67890');
        $doctorVerification->setExpiresAt(new \DateTime('+1 day'));
        $doctorVerification->setVerifiedAt(new \DateTime());
        $doctorVerification->setIpAddress('127.0.0.1');
        $doctorVerification->setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36');
        
        $manager->persist($doctorVerification);
        $this->addReference(self::VERIFICATION_DOCTOR_REFERENCE, $doctorVerification);

        // Patient user verification
        $patientUser = $this->getReference(UserFixtures::PATIENT_USER_REFERENCE, \App\Entity\User::class);
        
        $patientVerification = new UserVerification();
        $patientVerification->setUser($patientUser);
        $patientVerification->setVerificationType('email_verification');
        $patientVerification->setVerificationMethod('email');
        $patientVerification->setToken('patient_verification_token_abcde');
        $patientVerification->setExpiresAt(new \DateTime('+1 day'));
        $patientVerification->setVerifiedAt(new \DateTime());
        $patientVerification->setIpAddress('127.0.1');
        $patientVerification->setUserAgent('Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/537.36');
        
        $manager->persist($patientVerification);
        $this->addReference(self::VERIFICATION_PATIENT_REFERENCE, $patientVerification);

        // Unverified user verification
        $nurseUser = $this->getReference(UserFixtures::NURSE_USER_REFERENCE, \App\Entity\User::class);
        
        $nurseVerification = new UserVerification();
        $nurseVerification->setUser($nurseUser);
        $nurseVerification->setVerificationType('email_verification');
        $nurseVerification->setVerificationMethod('email');
        $nurseVerification->setToken('nurse_verification_token_fghij');
        $nurseVerification->setExpiresAt(new \DateTime('+1 day'));
        // Not verified yet - verifiedAt is null
        $nurseVerification->setIpAddress('127.0.0.1');
        $nurseVerification->setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        
        $manager->persist($nurseVerification);

        // Expired verification
        $receptionistUser = $this->getReference(UserFixtures::RECEPTIONIST_USER_REFERENCE, \App\Entity\User::class);
        
        $receptionistVerification = new UserVerification();
        $receptionistVerification->setUser($receptionistUser);
        $receptionistVerification->setVerificationType('email_verification');
        $receptionistVerification->setVerificationMethod('email');
        $receptionistVerification->setToken('receptionist_verification_token_klmno');
        $receptionistVerification->setExpiresAt(new \DateTime('-1 day')); // Expired
        // Not verified - verifiedAt is null
        $receptionistVerification->setIpAddress('127.0.0.1');
        $receptionistVerification->setUserAgent('Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36');
        
        $manager->persist($receptionistVerification);

        // Phone verification
        $inactiveUser = $manager->getRepository(\App\Entity\User::class)->findOneBy(['email' => '<EMAIL>']);
        
        $phoneVerification = new UserVerification();
        $phoneVerification->setUser($inactiveUser);
        $phoneVerification->setVerificationType('phone_verification');
        $phoneVerification->setVerificationMethod('phone');
        $phoneVerification->setToken('phone_verification_token_pqrst');
        $phoneVerification->setExpiresAt(new \DateTime('+1 day'));
        $phoneVerification->setVerifiedAt(new \DateTime());
        $phoneVerification->setIpAddress('127.0.0.1');
        $phoneVerification->setUserAgent('Mozilla/5.0 (Android 10; Mobile; rv:68.0) Gecko/68.0 Firefox/68.0');
        
        $manager->persist($phoneVerification);

        $manager->flush();
    }
}