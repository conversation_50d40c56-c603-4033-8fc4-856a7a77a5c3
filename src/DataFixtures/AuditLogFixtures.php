<?php

namespace App\DataFixtures;

use App\Entity\AuditLog;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;

class AuditLogFixtures extends Fixture implements DependentFixtureInterface
{
    public function getDependencies(): array
    {
        return [
            UserFixtures::class,
            PatientFixtures::class,
            DoctorFixtures::class,
        ];
    }

    public function load(ObjectManager $manager): void
    {
        // Admin user login audit log
        $adminUser = $this->getReference(UserFixtures::ADMIN_USER_REFERENCE, \App\Entity\User::class);
        
        $adminLoginAudit = new AuditLog();
        $adminLoginAudit->setUser($adminUser);
        $adminLoginAudit->setAction('LOGIN');
        $adminLoginAudit->setCategory('AUTHENTICATION');
        $adminLoginAudit->setResourceType('user');
        $adminLoginAudit->setIpAddress('127.0.0.1');
        $adminLoginAudit->setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        $adminLoginAudit->setSessionId('session_admin_12345');
        $adminLoginAudit->setSource('WEB');
        $adminLoginAudit->setSuccess(true);
        
        $manager->persist($adminLoginAudit);

        // Doctor user login audit log
        $doctorUser = $this->getReference(UserFixtures::DOCTOR_USER_REFERENCE, \App\Entity\User::class);
        
        $doctorLoginAudit = new AuditLog();
        $doctorLoginAudit->setUser($doctorUser);
        $doctorLoginAudit->setAction('LOGIN');
        $doctorLoginAudit->setCategory('AUTHENTICATION');
        $doctorLoginAudit->setResourceType('user');
        $doctorLoginAudit->setIpAddress('127.0.0.1');
        $doctorLoginAudit->setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36');
        $doctorLoginAudit->setSessionId('session_doctor_67890');
        $doctorLoginAudit->setSource('WEB');
        $doctorLoginAudit->setSuccess(true);
        
        $manager->persist($doctorLoginAudit);

        // Patient user login audit log
        $patientUser = $this->getReference(UserFixtures::PATIENT_USER_REFERENCE, \App\Entity\User::class);
        
        $patientLoginAudit = new AuditLog();
        $patientLoginAudit->setUser($patientUser);
        $patientLoginAudit->setAction('LOGIN');
        $patientLoginAudit->setCategory('AUTHENTICATION');
        $patientLoginAudit->setResourceType('user');
        $patientLoginAudit->setIpAddress('127.0.0.1');
        $patientLoginAudit->setUserAgent('Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/537.36');
        $patientLoginAudit->setSessionId('session_patient_abcde');
        $patientLoginAudit->setSource('MOBILE');
        $patientLoginAudit->setSuccess(true);
        
        $manager->persist($patientLoginAudit);

        // Failed login attempt
        $nurseUser = $this->getReference(UserFixtures::NURSE_USER_REFERENCE, \App\Entity\User::class);
        
        $failedLoginAudit = new AuditLog();
        $failedLoginAudit->setUser($nurseUser);
        $failedLoginAudit->setAction('FAILED_LOGIN');
        $failedLoginAudit->setCategory('AUTHENTICATION');
        $failedLoginAudit->setResourceType('user');
        $failedLoginAudit->setIpAddress('127.0.0.1');
        $failedLoginAudit->setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        $failedLoginAudit->setSessionId('session_nurse_fghij');
        $failedLoginAudit->setSource('WEB');
        $failedLoginAudit->setSuccess(false);
        
        $manager->persist($failedLoginAudit);

        // Patient record creation
        $patient = $this->getReference(PatientFixtures::PATIENT_JANE_REFERENCE, \App\Entity\Patient::class);
        
        $patientCreateAudit = new AuditLog();
        $patientCreateAudit->setUser($adminUser);
        $patientCreateAudit->setAction('CREATE');
        $patientCreateAudit->setCategory('DATA_MANAGEMENT');
        $patientCreateAudit->setResourceType('patient');
        $patientCreateAudit->setResourceId($patient->getId());
        $patientCreateAudit->setNewValues([
            'firstName' => 'Jane',
            'lastName' => 'Doe',
            'dateOfBirth' => '1990-05-15',
            'gender' => 'female'
        ]);
        $patientCreateAudit->setIpAddress('127.0.0.1');
        $patientCreateAudit->setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        $patientCreateAudit->setSessionId('session_admin_12345');
        $patientCreateAudit->setSource('WEB');
        $patientCreateAudit->setSuccess(true);
        
        $manager->persist($patientCreateAudit);

        // Patient record update
        $patientUpdateAudit = new AuditLog();
        $doctorUser = $this->getReference(UserFixtures::DOCTOR_USER_REFERENCE, \App\Entity\User::class);
        
        $patientUpdateAudit->setUser($doctorUser);
        $patientUpdateAudit->setAction('UPDATE');
        $patientUpdateAudit->setCategory('DATA_MANAGEMENT');
        $patientUpdateAudit->setResourceType('patient');
        $patientUpdateAudit->setResourceId($patient->getId());
        $patientUpdateAudit->setOldValues([
            'weight' => '59.00'
        ]);
        $patientUpdateAudit->setNewValues([
            'weight' => '60.00'
        ]);
        $patientUpdateAudit->setIpAddress('127.0.0.1');
        $patientUpdateAudit->setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36');
        $patientUpdateAudit->setSessionId('session_doctor_67890');
        $patientUpdateAudit->setSource('WEB');
        $patientUpdateAudit->setSuccess(true);
        
        $manager->persist($patientUpdateAudit);

        // Doctor profile update
        $doctor = $this->getReference(DoctorFixtures::DOCTOR_JOHN_REFERENCE, \App\Entity\Doctor::class);
        
        $doctorUpdateAudit = new AuditLog();
        $adminUser = $this->getReference(UserFixtures::ADMIN_USER_REFERENCE, \App\Entity\User::class);
        
        $doctorUpdateAudit->setUser($adminUser);
        $doctorUpdateAudit->setAction('UPDATE');
        $doctorUpdateAudit->setCategory('DATA_MANAGEMENT');
        $doctorUpdateAudit->setResourceType('doctor');
        $doctorUpdateAudit->setResourceId($doctor->getId());
        $doctorUpdateAudit->setOldValues([
            'consultationFee' => '140.00'
        ]);
        $doctorUpdateAudit->setNewValues([
            'consultationFee' => '150.00'
        ]);
        $doctorUpdateAudit->setIpAddress('127.0.0.1');
        $doctorUpdateAudit->setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        $doctorUpdateAudit->setSessionId('session_admin_12345');
        $doctorUpdateAudit->setSource('WEB');
        $doctorUpdateAudit->setSuccess(true);
        
        $manager->persist($doctorUpdateAudit);

        // User role assignment
        $nurseUserRoleAudit = new AuditLog();
        $adminUser = $this->getReference(UserFixtures::ADMIN_USER_REFERENCE, \App\Entity\User::class);
        
        $nurseUserRoleAudit->setUser($adminUser);
        $nurseUserRoleAudit->setAction('ROLE_ASSIGN');
        $nurseUserRoleAudit->setCategory('USER_MANAGEMENT');
        $nurseUserRoleAudit->setResourceType('user');
        $nurseUserRoleAudit->setResourceId($nurseUser->getId());
        $nurseUserRoleAudit->setNewValues([
            'role' => 'ROLE_NURSE'
        ]);
        $nurseUserRoleAudit->setIpAddress('127.0.0.1');
        $nurseUserRoleAudit->setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        $nurseUserRoleAudit->setSessionId('session_admin_12345');
        $nurseUserRoleAudit->setSource('WEB');
        $nurseUserRoleAudit->setSuccess(true);
        
        $manager->persist($nurseUserRoleAudit);

        $manager->flush();
    }
}