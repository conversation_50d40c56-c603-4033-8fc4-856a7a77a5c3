<?php

namespace App\DataFixtures;

use App\Entity\Doctor;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;

class DoctorFixtures extends Fixture implements DependentFixtureInterface
{
    public const DOCTOR_JOHN_REFERENCE = 'doctor-john';
    public const DOCTOR_JANE_REFERENCE = 'doctor-jane';
    public const DOCTOR_MICHAEL_REFERENCE = 'doctor-michael';
    public const DOCTOR_EMILY_REFERENCE = 'doctor-emily';
    public const DOCTOR_DAVID_REFERENCE = 'doctor-david';

    public function getDependencies(): array
    {
        return [
            UserFixtures::class,
        ];
    }

    public function load(ObjectManager $manager): void
    {
        // Create cardiologist doctor
        $cardiologist = new Doctor();
        $cardiologist->setPrefix('Dr.');
        $cardiologist->setFirstName('John');
        $cardiologist->setLastName('Cardio');
        $cardiologist->setSuffix('MD');
        $cardiologist->setRegistrationNumber('REG-001');
        $cardiologist->setRegistrationExpiryDate(new \DateTime('2030-12-31'));
        $cardiologist->setEducation([
            [
                'institution' => 'Harvard Medical School',
                'degree' => 'MD',
                'graduation_year' => 2010
            ],
            [
                'institution' => 'Johns Hopkins Hospital',
                'degree' => 'Fellowship in Cardiology',
                'graduation_year' => 2015
            ]
        ]);
        $cardiologist->setDegree('MD');
        $cardiologist->setSpecialization('Cardiology');
        $cardiologist->setYearsOfExperience(15);
        $cardiologist->setBio('Dr. John Cardio is a renowned cardiologist with over 15 years of experience in treating heart conditions. He specializes in interventional cardiology and has performed over 1000 cardiac procedures.');
        $cardiologist->setPicture('/images/doctors/john_cardio.jpg');
        $cardiologist->setConsultationFee('150.00');
        $cardiologist->setAvailabilitySchedule([
            'monday' => ['09:00-12:00', '14:0-17:00'],
            'tuesday' => ['09:00-12:00', '14:00-17:00'],
            'wednesday' => ['09:00-12:00', '14:00-17:0'],
            'thursday' => ['09:00-12:00', '14:00-17:00'],
            'friday' => ['09:00-12:00', '14:00-17:00'],
            'saturday' => ['09:00-12:00'],
            'sunday' => []
        ]);
        $cardiologist->setStatus('active');
        
        // Link to doctor user
        $doctorUser = $this->getReference(UserFixtures::DOCTOR_USER_REFERENCE, \App\Entity\User::class);
        $cardiologist->setUser($doctorUser);
        $doctorUser->setDoctor($cardiologist);
        
        $manager->persist($cardiologist);
        $this->addReference(self::DOCTOR_JOHN_REFERENCE, $cardiologist);

        // Create pediatrician doctor
        $pediatrician = new Doctor();
        $pediatrician->setPrefix('Dr.');
        $pediatrician->setFirstName('Jane');
        $pediatrician->setLastName('Pediatric');
        $pediatrician->setSuffix('MD');
        $pediatrician->setRegistrationNumber('REG-002');
        $pediatrician->setRegistrationExpiryDate(new \DateTime('2031-06-30'));
        $pediatrician->setEducation([
            [
                'institution' => 'Stanford University School of Medicine',
                'degree' => 'MD',
                'graduation_year' => 2012
            ],
            [
                'institution' => 'Children\'s Hospital of Philadelphia',
                'degree' => 'Residency in Pediatrics',
                'graduation_year' => 2016
            ]
        ]);
        $pediatrician->setDegree('MD');
        $pediatrician->setSpecialization('Pediatrics');
        $pediatrician->setYearsOfExperience(12);
        $pediatrician->setBio('Dr. Jane Pediatric is a dedicated pediatrician with 12 years of experience in child healthcare. She focuses on preventive care and developmental assessments for children from birth to adolescence.');
        $pediatrician->setPicture('/images/doctors/jane_pediatric.jpg');
        $pediatrician->setConsultationFee('120.00');
        $pediatrician->setAvailabilitySchedule([
            'monday' => ['10:00-13:00', '15:00-18:00'],
            'tuesday' => ['10:00-13:00', '15:00-18:00'],
            'wednesday' => ['10:00-13:00', '15:00-18:0'],
            'thursday' => ['10:00-13:00', '15:00-18:00'],
            'friday' => ['10:00-13:00', '15:00-18:00'],
            'saturday' => ['10:00-13:00'],
            'sunday' => []
        ]);
        $pediatrician->setStatus('active');
        
        $manager->persist($pediatrician);
        $this->addReference(self::DOCTOR_JANE_REFERENCE, $pediatrician);

        // Create orthopedic surgeon
        $orthopedic = new Doctor();
        $orthopedic->setPrefix('Dr.');
        $orthopedic->setFirstName('Michael');
        $orthopedic->setLastName('Ortho');
        $orthopedic->setSuffix('MD');
        $orthopedic->setRegistrationNumber('REG-003');
        $orthopedic->setRegistrationExpiryDate(new \DateTime('2029-09-30'));
        $orthopedic->setEducation([
            [
                'institution' => 'Yale School of Medicine',
                'degree' => 'MD',
                'graduation_year' => 2008
            ],
            [
                'institution' => 'Mayo Clinic',
                'degree' => 'Residency in Orthopedic Surgery',
                'graduation_year' => 2013
            ]
        ]);
        $orthopedic->setDegree('MD');
        $orthopedic->setSpecialization('Orthopedic Surgery');
        $orthopedic->setYearsOfExperience(17);
        $orthopedic->setBio('Dr. Michael Ortho is an experienced orthopedic surgeon specializing in joint replacement and sports medicine. He has successfully performed over 2000 orthopedic surgeries.');
        $orthopedic->setPicture('/images/doctors/michael_ortho.jpg');
        $orthopedic->setConsultationFee('200.00');
        $orthopedic->setAvailabilitySchedule([
            'monday' => ['08:00-12:00'],
            'tuesday' => ['08:00-12:00', '14:0-18:00'],
            'wednesday' => ['08:00-12:00'],
            'thursday' => ['08:00-12:00', '14:00-18:00'],
            'friday' => ['08:00-12:00'],
            'saturday' => [],
            'sunday' => []
        ]);
        $orthopedic->setStatus('active');
        
        $manager->persist($orthopedic);
        $this->addReference(self::DOCTOR_MICHAEL_REFERENCE, $orthopedic);

        // Create dermatologist
        $dermatologist = new Doctor();
        $dermatologist->setPrefix('Dr.');
        $dermatologist->setFirstName('Emily');
        $dermatologist->setLastName('Dermato');
        $dermatologist->setSuffix('MD');
        $dermatologist->setRegistrationNumber('REG-004');
        $dermatologist->setRegistrationExpiryDate(new \DateTime('2032-03-31'));
        $dermatologist->setEducation([
            [
                'institution' => 'University of California, San Francisco',
                'degree' => 'MD',
                'graduation_year' => 2014
            ],
            [
                'institution' => 'Cleveland Clinic',
                'degree' => 'Residency in Dermatology',
                'graduation_year' => 2018
            ]
        ]);
        $dermatologist->setDegree('MD');
        $dermatologist->setSpecialization('Dermatology');
        $dermatologist->setYearsOfExperience(10);
        $dermatologist->setBio('Dr. Emily Dermato is a skilled dermatologist with expertise in cosmetic dermatology and skin cancer treatment. She has published numerous research papers on dermatological conditions.');
        $dermatologist->setPicture('/images/doctors/emily_dermato.jpg');
        $dermatologist->setConsultationFee('130.00');
        $dermatologist->setAvailabilitySchedule([
            'monday' => ['11:00-15:00'],
            'tuesday' => ['11:00-15:00'],
            'wednesday' => ['11:00-15:00'],
            'thursday' => ['11:00-15:00'],
            'friday' => ['11:00-15:00'],
            'saturday' => ['09:00-13:00'],
            'sunday' => []
        ]);
        $dermatologist->setStatus('active');
        
        $manager->persist($dermatologist);
        $this->addReference(self::DOCTOR_EMILY_REFERENCE, $dermatologist);

        // Create general practitioner
        $gp = new Doctor();
        $gp->setPrefix('Dr.');
        $gp->setFirstName('David');
        $gp->setLastName('General');
        $gp->setSuffix('MBBS');
        $gp->setRegistrationNumber('REG-005');
        $gp->setRegistrationExpiryDate(new \DateTime('2030-11-30'));
        $gp->setEducation([
            [
                'institution' => 'Johns Hopkins University School of Medicine',
                'degree' => 'MBBS',
                'graduation_year' => 2011
            ],
            [
                'institution' => 'Massachusetts General Hospital',
                'degree' => 'Residency in Family Medicine',
                'graduation_year' => 2016
            ]
        ]);
        $gp->setDegree('MBBS');
        $gp->setSpecialization('General Practice');
        $gp->setYearsOfExperience(13);
        $gp->setBio('Dr. David General is a trusted family physician with 13 years of experience in comprehensive healthcare. He provides preventive care, chronic disease management, and health education to patients of all ages.');
        $gp->setPicture('/images/doctors/david_general.jpg');
        $gp->setConsultationFee('100.00');
        $gp->setAvailabilitySchedule([
            'monday' => ['08:00-17:00'],
            'tuesday' => ['08:00-17:00'],
            'wednesday' => ['08:00-17:00'],
            'thursday' => ['08:00-17:00'],
            'friday' => ['08:00-17:00'],
            'saturday' => ['08:00-12:00'],
            'sunday' => []
        ]);
        $gp->setStatus('active');
        
        $manager->persist($gp);
        $this->addReference(self::DOCTOR_DAVID_REFERENCE, $gp);

        // Create an inactive doctor
        $inactiveDoctor = new Doctor();
        $inactiveDoctor->setPrefix('Dr.');
        $inactiveDoctor->setFirstName('Inactive');
        $inactiveDoctor->setLastName('Doctor');
        $inactiveDoctor->setSuffix('MD');
        $inactiveDoctor->setRegistrationNumber('REG-006');
        $inactiveDoctor->setRegistrationExpiryDate(new \DateTime('2025-12-31'));
        $inactiveDoctor->setEducation([
            [
                'institution' => 'Inactive Medical School',
                'degree' => 'MD',
                'graduation_year' => 2010
            ]
        ]);
        $inactiveDoctor->setDegree('MD');
        $inactiveDoctor->setSpecialization('Inactive Specialization');
        $inactiveDoctor->setYearsOfExperience(5);
        $inactiveDoctor->setBio('This is an inactive doctor profile.');
        $inactiveDoctor->setStatus('inactive');
        
        $manager->persist($inactiveDoctor);

        $manager->flush();
    }
}