<?php

namespace App\DataFixtures;

use App\Entity\Permission;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

class PermissionFixtures extends Fixture
{
    // User permissions
    public const PERMISSION_USER_CREATE_REFERENCE = 'permission-user-create';
    public const PERMISSION_USER_READ_REFERENCE = 'permission-user-read';
    public const PERMISSION_USER_UPDATE_REFERENCE = 'permission-user-update';
    public const PERMISSION_USER_DELETE_REFERENCE = 'permission-user-delete';
    
    // Patient permissions
    public const PERMISSION_PATIENT_CREATE_REFERENCE = 'permission-patient-create';
    public const PERMISSION_PATIENT_READ_REFERENCE = 'permission-patient-read';
    public const PERMISSION_PATIENT_UPDATE_REFERENCE = 'permission-patient-update';
    public const PERMISSION_PATIENT_DELETE_REFERENCE = 'permission-patient-delete';
    
    // Doctor permissions
    public const PERMISSION_DOCTOR_CREATE_REFERENCE = 'permission-doctor-create';
    public const PERMISSION_DOCTOR_READ_REFERENCE = 'permission-doctor-read';
    public const PERMISSION_DOCTOR_UPDATE_REFERENCE = 'permission-doctor-update';
    public const PERMISSION_DOCTOR_DELETE_REFERENCE = 'permission-doctor-delete';
    
    // Appointment permissions
    public const PERMISSION_APPOINTMENT_CREATE_REFERENCE = 'permission-appointment-create';
    public const PERMISSION_APPOINTMENT_READ_REFERENCE = 'permission-appointment-read';
    public const PERMISSION_APPOINTMENT_UPDATE_REFERENCE = 'permission-appointment-update';
    public const PERMISSION_APPOINTMENT_DELETE_REFERENCE = 'permission-appointment-delete';
    
    // Medical record permissions
    public const PERMISSION_MEDICAL_RECORD_CREATE_REFERENCE = 'permission-medical-record-create';
    public const PERMISSION_MEDICAL_RECORD_READ_REFERENCE = 'permission-medical-record-read';
    public const PERMISSION_MEDICAL_RECORD_UPDATE_REFERENCE = 'permission-medical-record-update';
    public const PERMISSION_MEDICAL_RECORD_DELETE_REFERENCE = 'permission-medical-record-delete';
    
    // Test result permissions
    public const PERMISSION_TEST_RESULT_CREATE_REFERENCE = 'permission-test-result-create';
    public const PERMISSION_TEST_RESULT_READ_REFERENCE = 'permission-test-result-read';
    public const PERMISSION_TEST_RESULT_UPDATE_REFERENCE = 'permission-test-result-update';
    public const PERMISSION_TEST_RESULT_DELETE_REFERENCE = 'permission-test-result-delete';
    
    // Prescription permissions
    public const PERMISSION_PRESCRIPTION_CREATE_REFERENCE = 'permission-prescription-create';
    public const PERMISSION_PRESCRIPTION_READ_REFERENCE = 'permission-prescription-read';
    public const PERMISSION_PRESCRIPTION_UPDATE_REFERENCE = 'permission-prescription-update';
    public const PERMISSION_PRESCRIPTION_DELETE_REFERENCE = 'permission-prescription-delete';
    
    // Billing permissions
    public const PERMISSION_BILLING_CREATE_REFERENCE = 'permission-billing-create';
    public const PERMISSION_BILLING_READ_REFERENCE = 'permission-billing-read';
    public const PERMISSION_BILLING_UPDATE_REFERENCE = 'permission-billing-update';
    public const PERMISSION_BILLING_DELETE_REFERENCE = 'permission-billing-delete';

    public function load(ObjectManager $manager): void
    {
        // User permissions
        $userCreate = new Permission();
        $userCreate->setName('USER_CREATE');
        $userCreate->setDisplayName('Create Users');
        $userCreate->setDescription('Ability to create new user accounts');
        $userCreate->setResource('user');
        $userCreate->setAction('create');
        $userCreate->setIsSystemPermission(true);
        
        $manager->persist($userCreate);
        $this->addReference(self::PERMISSION_USER_CREATE_REFERENCE, $userCreate);

        $userRead = new Permission();
        $userRead->setName('USER_READ');
        $userRead->setDisplayName('Read Users');
        $userRead->setDescription('Ability to view user accounts');
        $userRead->setResource('user');
        $userRead->setAction('read');
        $userRead->setIsSystemPermission(true);
        
        $manager->persist($userRead);
        $this->addReference(self::PERMISSION_USER_READ_REFERENCE, $userRead);

        $userUpdate = new Permission();
        $userUpdate->setName('USER_UPDATE');
        $userUpdate->setDisplayName('Update Users');
        $userUpdate->setDescription('Ability to modify user accounts');
        $userUpdate->setResource('user');
        $userUpdate->setAction('update');
        $userUpdate->setIsSystemPermission(true);
        
        $manager->persist($userUpdate);
        $this->addReference(self::PERMISSION_USER_UPDATE_REFERENCE, $userUpdate);

        $userDelete = new Permission();
        $userDelete->setName('USER_DELETE');
        $userDelete->setDisplayName('Delete Users');
        $userDelete->setDescription('Ability to delete user accounts');
        $userDelete->setResource('user');
        $userDelete->setAction('delete');
        $userDelete->setIsSystemPermission(true);
        
        $manager->persist($userDelete);
        $this->addReference(self::PERMISSION_USER_DELETE_REFERENCE, $userDelete);

        // Patient permissions
        $patientCreate = new Permission();
        $patientCreate->setName('PATIENT_CREATE');
        $patientCreate->setDisplayName('Create Patients');
        $patientCreate->setDescription('Ability to register new patients');
        $patientCreate->setResource('patient');
        $patientCreate->setAction('create');
        $patientCreate->setIsSystemPermission(true);
        
        $manager->persist($patientCreate);
        $this->addReference(self::PERMISSION_PATIENT_CREATE_REFERENCE, $patientCreate);

        $patientRead = new Permission();
        $patientRead->setName('PATIENT_READ');
        $patientRead->setDisplayName('Read Patients');
        $patientRead->setDescription('Ability to view patient records');
        $patientRead->setResource('patient');
        $patientRead->setAction('read');
        $patientRead->setIsSystemPermission(true);
        
        $manager->persist($patientRead);
        $this->addReference(self::PERMISSION_PATIENT_READ_REFERENCE, $patientRead);

        $patientUpdate = new Permission();
        $patientUpdate->setName('PATIENT_UPDATE');
        $patientUpdate->setDisplayName('Update Patients');
        $patientUpdate->setDescription('Ability to modify patient records');
        $patientUpdate->setResource('patient');
        $patientUpdate->setAction('update');
        $patientUpdate->setIsSystemPermission(true);
        
        $manager->persist($patientUpdate);
        $this->addReference(self::PERMISSION_PATIENT_UPDATE_REFERENCE, $patientUpdate);

        $patientDelete = new Permission();
        $patientDelete->setName('PATIENT_DELETE');
        $patientDelete->setDisplayName('Delete Patients');
        $patientDelete->setDescription('Ability to delete patient records');
        $patientDelete->setResource('patient');
        $patientDelete->setAction('delete');
        $patientDelete->setIsSystemPermission(true);
        
        $manager->persist($patientDelete);
        $this->addReference(self::PERMISSION_PATIENT_DELETE_REFERENCE, $patientDelete);

        // Doctor permissions
        $doctorCreate = new Permission();
        $doctorCreate->setName('DOCTOR_CREATE');
        $doctorCreate->setDisplayName('Create Doctors');
        $doctorCreate->setDescription('Ability to register new doctors');
        $doctorCreate->setResource('doctor');
        $doctorCreate->setAction('create');
        $doctorCreate->setIsSystemPermission(true);
        
        $manager->persist($doctorCreate);
        $this->addReference(self::PERMISSION_DOCTOR_CREATE_REFERENCE, $doctorCreate);

        $doctorRead = new Permission();
        $doctorRead->setName('DOCTOR_READ');
        $doctorRead->setDisplayName('Read Doctors');
        $doctorRead->setDescription('Ability to view doctor profiles');
        $doctorRead->setResource('doctor');
        $doctorRead->setAction('read');
        $doctorRead->setIsSystemPermission(true);
        
        $manager->persist($doctorRead);
        $this->addReference(self::PERMISSION_DOCTOR_READ_REFERENCE, $doctorRead);

        $doctorUpdate = new Permission();
        $doctorUpdate->setName('DOCTOR_UPDATE');
        $doctorUpdate->setDisplayName('Update Doctors');
        $doctorUpdate->setDescription('Ability to modify doctor profiles');
        $doctorUpdate->setResource('doctor');
        $doctorUpdate->setAction('update');
        $doctorUpdate->setIsSystemPermission(true);
        
        $manager->persist($doctorUpdate);
        $this->addReference(self::PERMISSION_DOCTOR_UPDATE_REFERENCE, $doctorUpdate);

        $doctorDelete = new Permission();
        $doctorDelete->setName('DOCTOR_DELETE');
        $doctorDelete->setDisplayName('Delete Doctors');
        $doctorDelete->setDescription('Ability to delete doctor profiles');
        $doctorDelete->setResource('doctor');
        $doctorDelete->setAction('delete');
        $doctorDelete->setIsSystemPermission(true);
        
        $manager->persist($doctorDelete);
        $this->addReference(self::PERMISSION_DOCTOR_DELETE_REFERENCE, $doctorDelete);

        // Appointment permissions
        $appointmentCreate = new Permission();
        $appointmentCreate->setName('APPOINTMENT_CREATE');
        $appointmentCreate->setDisplayName('Create Appointments');
        $appointmentCreate->setDescription('Ability to schedule new appointments');
        $appointmentCreate->setResource('appointment');
        $appointmentCreate->setAction('create');
        $appointmentCreate->setIsSystemPermission(true);
        
        $manager->persist($appointmentCreate);
        $this->addReference(self::PERMISSION_APPOINTMENT_CREATE_REFERENCE, $appointmentCreate);

        $appointmentRead = new Permission();
        $appointmentRead->setName('APPOINTMENT_READ');
        $appointmentRead->setDisplayName('Read Appointments');
        $appointmentRead->setDescription('Ability to view appointments');
        $appointmentRead->setResource('appointment');
        $appointmentRead->setAction('read');
        $appointmentRead->setIsSystemPermission(true);
        
        $manager->persist($appointmentRead);
        $this->addReference(self::PERMISSION_APPOINTMENT_READ_REFERENCE, $appointmentRead);

        $appointmentUpdate = new Permission();
        $appointmentUpdate->setName('APPOINTMENT_UPDATE');
        $appointmentUpdate->setDisplayName('Update Appointments');
        $appointmentUpdate->setDescription('Ability to modify appointments');
        $appointmentUpdate->setResource('appointment');
        $appointmentUpdate->setAction('update');
        $appointmentUpdate->setIsSystemPermission(true);
        
        $manager->persist($appointmentUpdate);
        $this->addReference(self::PERMISSION_APPOINTMENT_UPDATE_REFERENCE, $appointmentUpdate);

        $appointmentDelete = new Permission();
        $appointmentDelete->setName('APPOINTMENT_DELETE');
        $appointmentDelete->setDisplayName('Delete Appointments');
        $appointmentDelete->setDescription('Ability to cancel appointments');
        $appointmentDelete->setResource('appointment');
        $appointmentDelete->setAction('delete');
        $appointmentDelete->setIsSystemPermission(true);
        
        $manager->persist($appointmentDelete);
        $this->addReference(self::PERMISSION_APPOINTMENT_DELETE_REFERENCE, $appointmentDelete);

        // Medical record permissions
        $medicalRecordCreate = new Permission();
        $medicalRecordCreate->setName('MEDICAL_RECORD_CREATE');
        $medicalRecordCreate->setDisplayName('Create Medical Records');
        $medicalRecordCreate->setDescription('Ability to create new medical records');
        $medicalRecordCreate->setResource('medical_record');
        $medicalRecordCreate->setAction('create');
        $medicalRecordCreate->setIsSystemPermission(true);
        
        $manager->persist($medicalRecordCreate);
        $this->addReference(self::PERMISSION_MEDICAL_RECORD_CREATE_REFERENCE, $medicalRecordCreate);

        $medicalRecordRead = new Permission();
        $medicalRecordRead->setName('MEDICAL_RECORD_READ');
        $medicalRecordRead->setDisplayName('Read Medical Records');
        $medicalRecordRead->setDescription('Ability to view medical records');
        $medicalRecordRead->setResource('medical_record');
        $medicalRecordRead->setAction('read');
        $medicalRecordRead->setIsSystemPermission(true);
        
        $manager->persist($medicalRecordRead);
        $this->addReference(self::PERMISSION_MEDICAL_RECORD_READ_REFERENCE, $medicalRecordRead);

        $medicalRecordUpdate = new Permission();
        $medicalRecordUpdate->setName('MEDICAL_RECORD_UPDATE');
        $medicalRecordUpdate->setDisplayName('Update Medical Records');
        $medicalRecordUpdate->setDescription('Ability to modify medical records');
        $medicalRecordUpdate->setResource('medical_record');
        $medicalRecordUpdate->setAction('update');
        $medicalRecordUpdate->setIsSystemPermission(true);
        
        $manager->persist($medicalRecordUpdate);
        $this->addReference(self::PERMISSION_MEDICAL_RECORD_UPDATE_REFERENCE, $medicalRecordUpdate);

        $medicalRecordDelete = new Permission();
        $medicalRecordDelete->setName('MEDICAL_RECORD_DELETE');
        $medicalRecordDelete->setDisplayName('Delete Medical Records');
        $medicalRecordDelete->setDescription('Ability to delete medical records');
        $medicalRecordDelete->setResource('medical_record');
        $medicalRecordDelete->setAction('delete');
        $medicalRecordDelete->setIsSystemPermission(true);
        
        $manager->persist($medicalRecordDelete);
        $this->addReference(self::PERMISSION_MEDICAL_RECORD_DELETE_REFERENCE, $medicalRecordDelete);

        // Test result permissions
        $testResultCreate = new Permission();
        $testResultCreate->setName('TEST_RESULT_CREATE');
        $testResultCreate->setDisplayName('Create Test Results');
        $testResultCreate->setDescription('Ability to record new test results');
        $testResultCreate->setResource('test_result');
        $testResultCreate->setAction('create');
        $testResultCreate->setIsSystemPermission(true);
        
        $manager->persist($testResultCreate);
        $this->addReference(self::PERMISSION_TEST_RESULT_CREATE_REFERENCE, $testResultCreate);

        $testResultRead = new Permission();
        $testResultRead->setName('TEST_RESULT_READ');
        $testResultRead->setDisplayName('Read Test Results');
        $testResultRead->setDescription('Ability to view test results');
        $testResultRead->setResource('test_result');
        $testResultRead->setAction('read');
        $testResultRead->setIsSystemPermission(true);
        
        $manager->persist($testResultRead);
        $this->addReference(self::PERMISSION_TEST_RESULT_READ_REFERENCE, $testResultRead);

        $testResultUpdate = new Permission();
        $testResultUpdate->setName('TEST_RESULT_UPDATE');
        $testResultUpdate->setDisplayName('Update Test Results');
        $testResultUpdate->setDescription('Ability to modify test results');
        $testResultUpdate->setResource('test_result');
        $testResultUpdate->setAction('update');
        $testResultUpdate->setIsSystemPermission(true);
        
        $manager->persist($testResultUpdate);
        $this->addReference(self::PERMISSION_TEST_RESULT_UPDATE_REFERENCE, $testResultUpdate);

        $testResultDelete = new Permission();
        $testResultDelete->setName('TEST_RESULT_DELETE');
        $testResultDelete->setDisplayName('Delete Test Results');
        $testResultDelete->setDescription('Ability to delete test results');
        $testResultDelete->setResource('test_result');
        $testResultDelete->setAction('delete');
        $testResultDelete->setIsSystemPermission(true);
        
        $manager->persist($testResultDelete);
        $this->addReference(self::PERMISSION_TEST_RESULT_DELETE_REFERENCE, $testResultDelete);

        // Prescription permissions
        $prescriptionCreate = new Permission();
        $prescriptionCreate->setName('PRESCRIPTION_CREATE');
        $prescriptionCreate->setDisplayName('Create Prescriptions');
        $prescriptionCreate->setDescription('Ability to write new prescriptions');
        $prescriptionCreate->setResource('prescription');
        $prescriptionCreate->setAction('create');
        $prescriptionCreate->setIsSystemPermission(true);
        
        $manager->persist($prescriptionCreate);
        $this->addReference(self::PERMISSION_PRESCRIPTION_CREATE_REFERENCE, $prescriptionCreate);

        $prescriptionRead = new Permission();
        $prescriptionRead->setName('PRESCRIPTION_READ');
        $prescriptionRead->setDisplayName('Read Prescriptions');
        $prescriptionRead->setDescription('Ability to view prescriptions');
        $prescriptionRead->setResource('prescription');
        $prescriptionRead->setAction('read');
        $prescriptionRead->setIsSystemPermission(true);
        
        $manager->persist($prescriptionRead);
        $this->addReference(self::PERMISSION_PRESCRIPTION_READ_REFERENCE, $prescriptionRead);

        $prescriptionUpdate = new Permission();
        $prescriptionUpdate->setName('PRESCRIPTION_UPDATE');
        $prescriptionUpdate->setDisplayName('Update Prescriptions');
        $prescriptionUpdate->setDescription('Ability to modify prescriptions');
        $prescriptionUpdate->setResource('prescription');
        $prescriptionUpdate->setAction('update');
        $prescriptionUpdate->setIsSystemPermission(true);
        
        $manager->persist($prescriptionUpdate);
        $this->addReference(self::PERMISSION_PRESCRIPTION_UPDATE_REFERENCE, $prescriptionUpdate);

        $prescriptionDelete = new Permission();
        $prescriptionDelete->setName('PRESCRIPTION_DELETE');
        $prescriptionDelete->setDisplayName('Delete Prescriptions');
        $prescriptionDelete->setDescription('Ability to delete prescriptions');
        $prescriptionDelete->setResource('prescription');
        $prescriptionDelete->setAction('delete');
        $prescriptionDelete->setIsSystemPermission(true);
        
        $manager->persist($prescriptionDelete);
        $this->addReference(self::PERMISSION_PRESCRIPTION_DELETE_REFERENCE, $prescriptionDelete);

        // Billing permissions
        $billingCreate = new Permission();
        $billingCreate->setName('BILLING_CREATE');
        $billingCreate->setDisplayName('Create Bills');
        $billingCreate->setDescription('Ability to create new bills');
        $billingCreate->setResource('billing');
        $billingCreate->setAction('create');
        $billingCreate->setIsSystemPermission(true);
        
        $manager->persist($billingCreate);
        $this->addReference(self::PERMISSION_BILLING_CREATE_REFERENCE, $billingCreate);

        $billingRead = new Permission();
        $billingRead->setName('BILLING_READ');
        $billingRead->setDisplayName('Read Bills');
        $billingRead->setDescription('Ability to view bills');
        $billingRead->setResource('billing');
        $billingRead->setAction('read');
        $billingRead->setIsSystemPermission(true);
        
        $manager->persist($billingRead);
        $this->addReference(self::PERMISSION_BILLING_READ_REFERENCE, $billingRead);

        $billingUpdate = new Permission();
        $billingUpdate->setName('BILLING_UPDATE');
        $billingUpdate->setDisplayName('Update Bills');
        $billingUpdate->setDescription('Ability to modify bills');
        $billingUpdate->setResource('billing');
        $billingUpdate->setAction('update');
        $billingUpdate->setIsSystemPermission(true);
        
        $manager->persist($billingUpdate);
        $this->addReference(self::PERMISSION_BILLING_UPDATE_REFERENCE, $billingUpdate);

        $billingDelete = new Permission();
        $billingDelete->setName('BILLING_DELETE');
        $billingDelete->setDisplayName('Delete Bills');
        $billingDelete->setDescription('Ability to delete bills');
        $billingDelete->setResource('billing');
        $billingDelete->setAction('delete');
        $billingDelete->setIsSystemPermission(true);
        
        $manager->persist($billingDelete);
        $this->addReference(self::PERMISSION_BILLING_DELETE_REFERENCE, $billingDelete);

        $manager->flush();
    }
}