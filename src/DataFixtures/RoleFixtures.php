<?php

namespace App\DataFixtures;

use App\Entity\Role;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

class RoleFixtures extends Fixture
{
    public const ROLE_ADMIN_REFERENCE = 'role-admin';
    public const ROLE_DOCTOR_REFERENCE = 'role-doctor';
    public const ROLE_NURSE_REFERENCE = 'role-nurse';
    public const ROLE_PATIENT_REFERENCE = 'role-patient';
    public const ROLE_RECEPTIONIST_REFERENCE = 'role-receptionist';
    public const ROLE_LAB_TECHNICIAN_REFERENCE = 'role-lab-technician';
    public const ROLE_PHARMACIST_REFERENCE = 'role-pharmacist';

    public function load(ObjectManager $manager): void
    {
        // Create admin role
        $adminRole = new Role();
        $adminRole->setName('ROLE_ADMIN');
        $adminRole->setDisplayName('Administrator');
        $adminRole->setDescription('Full system access with all privileges');
        $adminRole->setIsSystemRole(true);
        
        $manager->persist($adminRole);
        $this->addReference(self::ROLE_ADMIN_REFERENCE, $adminRole);

        // Create doctor role
        $doctorRole = new Role();
        $doctorRole->setName('ROLE_DOCTOR');
        $doctorRole->setDisplayName('Doctor');
        $doctorRole->setDescription('Medical doctor with patient treatment privileges');
        $doctorRole->setIsSystemRole(true);
        
        $manager->persist($doctorRole);
        $this->addReference(self::ROLE_DOCTOR_REFERENCE, $doctorRole);

        // Create nurse role
        $nurseRole = new Role();
        $nurseRole->setName('ROLE_NURSE');
        $nurseRole->setDisplayName('Nurse');
        $nurseRole->setDescription('Nursing staff with patient care privileges');
        $nurseRole->setIsSystemRole(true);
        
        $manager->persist($nurseRole);
        $this->addReference(self::ROLE_NURSE_REFERENCE, $nurseRole);

        // Create patient role
        $patientRole = new Role();
        $patientRole->setName('ROLE_PATIENT');
        $patientRole->setDisplayName('Patient');
        $patientRole->setDescription('Patient with limited access to their own medical records');
        $patientRole->setIsSystemRole(true);
        
        $manager->persist($patientRole);
        $this->addReference(self::ROLE_PATIENT_REFERENCE, $patientRole);

        // Create receptionist role
        $receptionistRole = new Role();
        $receptionistRole->setName('ROLE_RECEPTIONIST');
        $receptionistRole->setDisplayName('Receptionist');
        $receptionistRole->setDescription('Reception staff with appointment scheduling privileges');
        $receptionistRole->setIsSystemRole(true);
        
        $manager->persist($receptionistRole);
        $this->addReference(self::ROLE_RECEPTIONIST_REFERENCE, $receptionistRole);

        // Create lab technician role
        $labTechnicianRole = new Role();
        $labTechnicianRole->setName('ROLE_LAB_TECHNICIAN');
        $labTechnicianRole->setDisplayName('Lab Technician');
        $labTechnicianRole->setDescription('Laboratory staff with test result management privileges');
        $labTechnicianRole->setIsSystemRole(true);
        
        $manager->persist($labTechnicianRole);
        $this->addReference(self::ROLE_LAB_TECHNICIAN_REFERENCE, $labTechnicianRole);

        // Create pharmacist role
        $pharmacistRole = new Role();
        $pharmacistRole->setName('ROLE_PHARMACIST');
        $pharmacistRole->setDisplayName('Pharmacist');
        $pharmacistRole->setDescription('Pharmacy staff with prescription management privileges');
        $pharmacistRole->setIsSystemRole(true);
        
        $manager->persist($pharmacistRole);
        $this->addReference(self::ROLE_PHARMACIST_REFERENCE, $pharmacistRole);

        // Create some non-system roles
        $billingRole = new Role();
        $billingRole->setName('ROLE_BILLING');
        $billingRole->setDisplayName('Billing Specialist');
        $billingRole->setDescription('Staff with financial and billing management privileges');
        $billingRole->setIsSystemRole(false);
        
        $manager->persist($billingRole);

        $hrRole = new Role();
        $hrRole->setName('ROLE_HR');
        $hrRole->setDisplayName('Human Resources');
        $hrRole->setDescription('HR staff with employee management privileges');
        $hrRole->setIsSystemRole(false);
        
        $manager->persist($hrRole);

        $manager->flush();
    }
}