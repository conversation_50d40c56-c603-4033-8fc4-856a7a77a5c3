<?php

namespace App\DataFixtures;

use App\Entity\UserNotification;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;

class UserNotificationFixtures extends Fixture implements DependentFixtureInterface
{
    public function getDependencies(): array
    {
        return [
            UserFixtures::class,
            PatientFixtures::class,
            DoctorFixtures::class,
        ];
    }

    public function load(ObjectManager $manager): void
    {
        // Admin user notifications
        $adminUser = $this->getReference(UserFixtures::ADMIN_USER_REFERENCE, \App\Entity\User::class);
        
        // Welcome notification
        $welcomeNotification = new UserNotification();
        $welcomeNotification->setUser($adminUser);
        $welcomeNotification->setTitle('Welcome to the System');
        $welcomeNotification->setMessage('Welcome to the Healthcare Management System. You have been granted administrative privileges.');
        $welcomeNotification->setType('info');
        $welcomeNotification->setPriority('medium');
        $welcomeNotification->setChannel('in_app');
        $welcomeNotification->setIsRead(false);
        $welcomeNotification->setSentAt(new \DateTime());
        $welcomeNotification->setExpiresAt(new \DateTime('+30 days'));
        $welcomeNotification->setCreatedBy($adminUser);
        
        $manager->persist($welcomeNotification);

        // Security alert notification
        $securityNotification = new UserNotification();
        $securityNotification->setUser($adminUser);
        $securityNotification->setTitle('Security Alert');
        $securityNotification->setMessage('Multiple failed login attempts detected on your account. Please review your security settings.');
        $securityNotification->setType('warning');
        $securityNotification->setPriority('high');
        $securityNotification->setChannel('email');
        $securityNotification->setIsRead(false);
        $securityNotification->setSentAt(new \DateTime());
        $securityNotification->setExpiresAt(new \DateTime('+7 days'));
        $securityNotification->setActionUrl('/security/settings');
        $securityNotification->setActionText('Review Settings');
        $securityNotification->setCreatedBy($adminUser);
        $securityNotification->setMetadata([
            'event_count' => 5,
            'ip_address' => '*************'
        ]);
        
        $manager->persist($securityNotification);

        // Doctor user notifications
        $doctorUser = $this->getReference(UserFixtures::DOCTOR_USER_REFERENCE, \App\Entity\User::class);
        
        // New patient assigned notification
        $newPatientNotification = new UserNotification();
        $newPatientNotification->setUser($doctorUser);
        $newPatientNotification->setTitle('New Patient Assigned');
        $newPatientNotification->setMessage('A new patient, Jane Doe, has been assigned to you.');
        $newPatientNotification->setType('info');
        $newPatientNotification->setPriority('medium');
        $newPatientNotification->setChannel('in_app');
        $newPatientNotification->setIsRead(false);
        $newPatientNotification->setSentAt(new \DateTime());
        $newPatientNotification->setExpiresAt(new \DateTime('+7 days'));
        $newPatientNotification->setActionUrl('/patients/1');
        $newPatientNotification->setActionText('View Patient');
        $newPatientNotification->setCreatedBy($adminUser);
        $newPatientNotification->setMetadata([
            'patient_id' => 1,
            'patient_name' => 'Jane Doe'
        ]);
        
        $manager->persist($newPatientNotification);

        // Appointment reminder notification
        $appointmentNotification = new UserNotification();
        $appointmentNotification->setUser($doctorUser);
        $appointmentNotification->setTitle('Appointment Reminder');
        $appointmentNotification->setMessage('You have an appointment with John Smith in 30 minutes.');
        $appointmentNotification->setType('info');
        $appointmentNotification->setPriority('high');
        $appointmentNotification->setChannel('sms');
        $appointmentNotification->setIsRead(false);
        $appointmentNotification->setSentAt(new \DateTime());
        $appointmentNotification->setExpiresAt(new \DateTime('+1 day'));
        $appointmentNotification->setActionUrl('/appointments/1');
        $appointmentNotification->setActionText('View Appointment');
        $appointmentNotification->setCreatedBy($adminUser);
        $appointmentNotification->setMetadata([
            'appointment_id' => 1,
            'patient_name' => 'John Smith',
            'appointment_time' => '10:00 AM'
        ]);
        
        $manager->persist($appointmentNotification);

        // Patient user notifications
        $patientUser = $this->getReference(UserFixtures::PATIENT_USER_REFERENCE, \App\Entity\User::class);
        
        // Test results available notification
        $testResultsNotification = new UserNotification();
        $testResultsNotification->setUser($patientUser);
        $testResultsNotification->setTitle('Test Results Available');
        $testResultsNotification->setMessage('Your blood test results are now available for review.');
        $testResultsNotification->setType('success');
        $testResultsNotification->setPriority('high');
        $testResultsNotification->setChannel('email');
        $testResultsNotification->setIsRead(false);
        $testResultsNotification->setSentAt(new \DateTime());
        $testResultsNotification->setExpiresAt(new \DateTime('+30 days'));
        $testResultsNotification->setActionUrl('/test-results/1');
        $testResultsNotification->setActionText('View Results');
        $testResultsNotification->setCreatedBy($doctorUser);
        $testResultsNotification->setMetadata([
            'test_id' => 1,
            'test_type' => 'Blood Test',
            'test_date' => '2025-01-15'
        ]);
        
        $manager->persist($testResultsNotification);

        // Appointment confirmation notification
        $appointmentConfirmation = new UserNotification();
        $appointmentConfirmation->setUser($patientUser);
        $appointmentConfirmation->setTitle('Appointment Confirmed');
        $appointmentConfirmation->setMessage('Your appointment with Dr. John Cardio on January 20, 2025 at 10:00 AM has been confirmed.');
        $appointmentConfirmation->setType('success');
        $appointmentConfirmation->setPriority('medium');
        $appointmentConfirmation->setChannel('in_app');
        $appointmentConfirmation->setIsRead(false);
        $appointmentConfirmation->setSentAt(new \DateTime());
        $appointmentConfirmation->setExpiresAt(new \DateTime('+30 days'));
        $appointmentConfirmation->setActionUrl('/appointments/2');
        $appointmentConfirmation->setActionText('View Appointment');
        $appointmentConfirmation->setCreatedBy($adminUser);
        $appointmentConfirmation->setMetadata([
            'appointment_id' => 2,
            'doctor_name' => 'Dr. John Cardio',
            'appointment_date' => '2025-01-20',
            'appointment_time' => '10:00 AM'
        ]);
        
        $manager->persist($appointmentConfirmation);

        // System maintenance notification
        $maintenanceNotification = new UserNotification();
        $maintenanceNotification->setUser($patientUser);
        $maintenanceNotification->setTitle('System Maintenance');
        $maintenanceNotification->setMessage('The system will be undergoing maintenance on January 25, 2025 from 2:00 AM to 4:00 AM. Some features may be temporarily unavailable.');
        $maintenanceNotification->setType('info');
        $maintenanceNotification->setPriority('low');
        $maintenanceNotification->setChannel('email');
        $maintenanceNotification->setIsRead(false);
        $maintenanceNotification->setSentAt(new \DateTime());
        $maintenanceNotification->setExpiresAt(new \DateTime('+30 days'));
        $maintenanceNotification->setCreatedBy($adminUser);
        $maintenanceNotification->setMetadata([
            'maintenance_date' => '2025-01-25',
            'maintenance_start' => '02:00',
            'maintenance_end' => '04:00'
        ]);
        
        $manager->persist($maintenanceNotification);

        // Nurse user notifications
        $nurseUser = $this->getReference(UserFixtures::NURSE_USER_REFERENCE, \App\Entity\User::class);
        
        // New task assigned notification
        $taskNotification = new UserNotification();
        $taskNotification->setUser($nurseUser);
        $taskNotification->setTitle('New Task Assigned');
        $taskNotification->setMessage('You have been assigned to assist with patient care for Jane Doe.');
        $taskNotification->setType('info');
        $taskNotification->setPriority('medium');
        $taskNotification->setChannel('in_app');
        $taskNotification->setIsRead(false);
        $taskNotification->setSentAt(new \DateTime());
        $taskNotification->setExpiresAt(new \DateTime('+7 days'));
        $taskNotification->setActionUrl('/tasks/1');
        $taskNotification->setActionText('View Task');
        $taskNotification->setCreatedBy($doctorUser);
        $taskNotification->setMetadata([
            'task_id' => 1,
            'patient_name' => 'Jane Doe',
            'assigned_by' => 'Dr. John Cardio'
        ]);
        
        $manager->persist($taskNotification);

        // Read notification
        $readNotification = new UserNotification();
        $readNotification->setUser($nurseUser);
        $readNotification->setTitle('Policy Update');
        $readNotification->setMessage('The hospital policies have been updated. Please review the changes.');
        $readNotification->setType('info');
        $readNotification->setPriority('low');
        $readNotification->setChannel('email');
        $readNotification->setIsRead(true);
        $readNotification->setReadAt(new \DateTime('-1 day'));
        $readNotification->setSentAt(new \DateTime('-2 days'));
        $readNotification->setExpiresAt(new \DateTime('+30 days'));
        $readNotification->setCreatedBy($adminUser);
        
        $manager->persist($readNotification);

        $manager->flush();
    }
}