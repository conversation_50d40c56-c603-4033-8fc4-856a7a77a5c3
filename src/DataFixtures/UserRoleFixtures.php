<?php

namespace App\DataFixtures;

use App\Entity\UserRole;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;

class UserRoleFixtures extends Fixture implements DependentFixtureInterface
{
    public function getDependencies(): array
    {
        return [
            UserFixtures::class,
            RoleFixtures::class,
        ];
    }

    public function load(ObjectManager $manager): void
    {
        // Admin user roles
        $adminUser = $this->getReference(UserFixtures::ADMIN_USER_REFERENCE, \App\Entity\User::class);
        $adminRole = $this->getReference(RoleFixtures::ROLE_ADMIN_REFERENCE, \App\Entity\Role::class);
        
        $adminUserRole = new UserRole();
        $adminUserRole->setUser($adminUser);
        $adminUserRole->setRole($adminRole);
        $adminUserRole->setAssignedBy($adminUser);
        $adminUserRole->setAssignedAt(new \DateTimeImmutable());
        $adminUserRole->setNotes('Initial admin role assignment');
        
        $manager->persist($adminUserRole);

        // Doctor user roles
        $doctorUser = $this->getReference(UserFixtures::DOCTOR_USER_REFERENCE, \App\Entity\User::class);
        $doctorRole = $this->getReference(RoleFixtures::ROLE_DOCTOR_REFERENCE, \App\Entity\Role::class);
        
        $doctorUserRole = new UserRole();
        $doctorUserRole->setUser($doctorUser);
        $doctorUserRole->setRole($doctorRole);
        $doctorUserRole->setAssignedBy($adminUser);
        $doctorUserRole->setAssignedAt(new \DateTimeImmutable());
        $doctorUserRole->setNotes('Initial doctor role assignment');
        
        $manager->persist($doctorUserRole);

        // Patient user roles
        $patientUser = $this->getReference(UserFixtures::PATIENT_USER_REFERENCE, \App\Entity\User::class);
        $patientRole = $this->getReference(RoleFixtures::ROLE_PATIENT_REFERENCE, \App\Entity\Role::class);
        
        $patientUserRole = new UserRole();
        $patientUserRole->setUser($patientUser);
        $patientUserRole->setRole($patientRole);
        $patientUserRole->setAssignedBy($adminUser);
        $patientUserRole->setAssignedAt(new \DateTimeImmutable());
        $patientUserRole->setNotes('Initial patient role assignment');
        
        $manager->persist($patientUserRole);

        // Nurse user roles
        $nurseUser = $this->getReference(UserFixtures::NURSE_USER_REFERENCE, \App\Entity\User::class);
        $nurseRole = $this->getReference(RoleFixtures::ROLE_NURSE_REFERENCE, \App\Entity\Role::class);
        
        $nurseUserRole = new UserRole();
        $nurseUserRole->setUser($nurseUser);
        $nurseUserRole->setRole($nurseRole);
        $nurseUserRole->setAssignedBy($adminUser);
        $nurseUserRole->setAssignedAt(new \DateTimeImmutable());
        $nurseUserRole->setNotes('Initial nurse role assignment');
        
        $manager->persist($nurseUserRole);

        // Receptionist user roles
        $receptionistUser = $this->getReference(UserFixtures::RECEPTIONIST_USER_REFERENCE, \App\Entity\User::class);
        $receptionistRole = $this->getReference(RoleFixtures::ROLE_RECEPTIONIST_REFERENCE, \App\Entity\Role::class);
        
        $receptionistUserRole = new UserRole();
        $receptionistUserRole->setUser($receptionistUser);
        $receptionistUserRole->setRole($receptionistRole);
        $receptionistUserRole->setAssignedBy($adminUser);
        $receptionistUserRole->setAssignedAt(new \DateTimeImmutable());
        $receptionistUserRole->setNotes('Initial receptionist role assignment');
        
        $manager->persist($receptionistUserRole);

        // Assign multiple roles to admin user
        $labTechnicianRole = $this->getReference(RoleFixtures::ROLE_LAB_TECHNICIAN_REFERENCE, \App\Entity\Role::class);
        
        $adminLabTechRole = new UserRole();
        $adminLabTechRole->setUser($adminUser);
        $adminLabTechRole->setRole($labTechnicianRole);
        $adminLabTechRole->setAssignedBy($adminUser);
        $adminLabTechRole->setAssignedAt(new \DateTimeImmutable());
        $adminLabTechRole->setNotes('Additional role for admin user');
        
        $manager->persist($adminLabTechRole);

        $manager->flush();
    }
}