<?php

namespace App\DataFixtures;

use App\Entity\User;
use App\Entity\UserProfile;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

class UserFixtures extends Fixture
{
    public const ADMIN_USER_REFERENCE = 'admin-user';
    public const DOCTOR_USER_REFERENCE = 'doctor-user';
    public const PATIENT_USER_REFERENCE = 'patient-user';
    public const NURSE_USER_REFERENCE = 'nurse-user';
    public const RECEPTIONIST_USER_REFERENCE = 'receptionist-user';

    private UserPasswordHasherInterface $passwordHasher;

    public function __construct(UserPasswordHasherInterface $passwordHasher)
    {
        $this->passwordHasher = $passwordHasher;
    }

    public function load(ObjectManager $manager): void
    {
        // Create admin user
        $adminUser = new User();
        $adminUser->setEmail('<EMAIL>');
        $adminUser->setPhone('+**********');
        $adminUser->setPassword($this->passwordHasher->hashPassword($adminUser, 'admin123'));
        $adminUser->setRoles(['ROLE_ADMIN']);
        $adminUser->setStatus('active');
        $adminUser->setLastLoginAt(new \DateTimeImmutable('2025-01-15 10:30:0'));
        
        $manager->persist($adminUser);
        $this->addReference(self::ADMIN_USER_REFERENCE, $adminUser);

        // Create doctor user
        $doctorUser = new User();
        $doctorUser->setEmail('<EMAIL>');
        $doctorUser->setPhone('+**********');
        $doctorUser->setPassword($this->passwordHasher->hashPassword($doctorUser, 'doctor123'));
        $doctorUser->setRoles(['ROLE_DOCTOR']);
        $doctorUser->setStatus('active');
        $doctorUser->setLastLoginAt(new \DateTimeImmutable('2025-01-15 09:15:00'));
        
        $manager->persist($doctorUser);
        $this->addReference(self::DOCTOR_USER_REFERENCE, $doctorUser);

        // Create patient user
        $patientUser = new User();
        $patientUser->setEmail('<EMAIL>');
        $patientUser->setPhone('+**********');
        $patientUser->setPassword($this->passwordHasher->hashPassword($patientUser, 'patient123'));
        $patientUser->setRoles(['ROLE_PATIENT']);
        $patientUser->setStatus('active');
        $patientUser->setLastLoginAt(new \DateTimeImmutable('2025-01-15 08:45:00'));
        
        $manager->persist($patientUser);
        $this->addReference(self::PATIENT_USER_REFERENCE, $patientUser);

        // Create nurse user
        $nurseUser = new User();
        $nurseUser->setEmail('<EMAIL>');
        $nurseUser->setPhone('+**********');
        $nurseUser->setPassword($this->passwordHasher->hashPassword($nurseUser, 'nurse123'));
        $nurseUser->setRoles(['ROLE_NURSE']);
        $nurseUser->setStatus('active');
        $nurseUser->setLastLoginAt(new \DateTimeImmutable('2025-01-15 07:30:00'));
        
        $manager->persist($nurseUser);
        $this->addReference(self::NURSE_USER_REFERENCE, $nurseUser);

        // Create receptionist user
        $receptionistUser = new User();
        $receptionistUser->setEmail('<EMAIL>');
        $receptionistUser->setPhone('+**********');
        $receptionistUser->setPassword($this->passwordHasher->hashPassword($receptionistUser, 'receptionist123'));
        $receptionistUser->setRoles(['ROLE_RECEPTIONIST']);
        $receptionistUser->setStatus('active');
        $receptionistUser->setLastLoginAt(new \DateTimeImmutable('2025-01-15 08:00:00'));
        
        $manager->persist($receptionistUser);
        $this->addReference(self::RECEPTIONIST_USER_REFERENCE, $receptionistUser);

        // Create some inactive users
        $inactiveUser = new User();
        $inactiveUser->setEmail('<EMAIL>');
        $inactiveUser->setPhone('+1234567895');
        $inactiveUser->setPassword($this->passwordHasher->hashPassword($inactiveUser, 'inactive123'));
        $inactiveUser->setRoles(['ROLE_USER']);
        $inactiveUser->setStatus('disabled');
        
        $manager->persist($inactiveUser);

        // Create pending user
        $pendingUser = new User();
        $pendingUser->setEmail('<EMAIL>');
        $pendingUser->setPhone('+1234567896');
        $pendingUser->setPassword($this->passwordHasher->hashPassword($pendingUser, 'pending123'));
        $pendingUser->setRoles(['ROLE_USER']);
        $pendingUser->setStatus('pending');
        
        $manager->persist($pendingUser);

        $manager->flush();
    }
}