<?php

namespace App\Controller\Api;

use App\Entity\User;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Address;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class AuthController extends AbstractController
{
    #[Route('/api/auth/register', name: 'api_auth_register', methods: ['POST'])]
    public function register(
        Request $request,
        UserPasswordHasherInterface $passwordHasher,
        EntityManagerInterface $entityManager,
        ValidatorInterface $validator
    ): JsonResponse {
        $data = json_decode($request->getContent(), true);
        
        if (!$data) {
            return new JsonResponse([
                'code' => 400,
                'message' => 'Invalid JSON data'
            ], 400);
        }
        
        $firstName = $data['firstName'] ?? '';
        $lastName = $data['lastName'] ?? '';
        $email = $data['email'] ?? '';
        $password = $data['password'] ?? '';
        $confirmPassword = $data['confirmPassword'] ?? '';
        
        // Validate required fields
        if (empty($firstName) || empty($lastName) || empty($email) || empty($password) || empty($confirmPassword)) {
            return new JsonResponse([
                'code' => 400,
                'message' => 'All fields are required'
            ], 400);
        }
        
        // Check if passwords match
        if ($password !== $confirmPassword) {
            return new JsonResponse([
                'code' => 400,
                'message' => 'Passwords do not match'
            ], 400);
        }
        
        // Check if user already exists
        $existingUser = $entityManager->getRepository(User::class)->findOneBy(['email' => $email]);
        if ($existingUser) {
            return new JsonResponse([
                'code' => 400,
                'message' => 'User with this email already exists'
            ], 400);
        }
        
        // Create user
        $user = new User();
        $user->setEmail($email);
        $user->setPassword($passwordHasher->hashPassword($user, $password));
        $user->setRoles(['ROLE_USER']);
        $user->setStatus('pending'); // Set to pending until email verification
        
        // Create user profile
        $profile = new \App\Entity\UserProfile();
        $profile->setFirstName($firstName);
        $profile->setLastName($lastName);
        $profile->setUser($user);
        $user->setProfile($profile);
        
        // Validate user
        $errors = $validator->validate($user);
        if (count($errors) > 0) {
            $errorMessages = [];
            foreach ($errors as $error) {
                $errorMessages[] = $error->getMessage();
            }
            
            return new JsonResponse([
                'code' => 400,
                'message' => 'Validation failed',
                'errors' => $errorMessages
            ], 400);
        }
        
        // Validate profile
        $profileErrors = $validator->validate($profile);
        if (count($profileErrors) > 0) {
            $errorMessages = [];
            foreach ($profileErrors as $error) {
                $errorMessages[] = $error->getMessage();
            }
            
            return new JsonResponse([
                'code' => 400,
                'message' => 'Profile validation failed',
                'errors' => $errorMessages
            ], 400);
        }
        
        // Persist user and profile
        $entityManager->persist($user);
        $entityManager->persist($profile);
        $entityManager->flush();
        
        return new JsonResponse([
            'message' => 'Registration successful! Please check your email for verification.'
        ], 201);
    }
    
    #[Route('/api/auth/forgot-password', name: 'api_auth_forgot_password', methods: ['POST'])]
    public function forgotPassword(
        Request $request,
        UserRepository $userRepository,
        MailerInterface $mailer
    ): JsonResponse {
        $data = json_decode($request->getContent(), true);
        
        if (!$data) {
            return new JsonResponse([
                'code' => 400,
                'message' => 'Invalid JSON data'
            ], 400);
        }
        
        $email = $data['email'] ?? '';
        
        if (empty($email)) {
            return new JsonResponse([
                'code' => 400,
                'message' => 'Email is required'
            ], 400);
        }
        
        $user = $userRepository->findOneBy(['email' => $email]);
        
        if ($user) {
            // Generate a reset token (in a real app, you'd store this in the database)
            $resetToken = bin2hex(random_bytes(32));
            // In a real app, you'd save this token with an expiration
            
            // For demonstration purposes, we'll just send an email saying to contact support
            $emailMessage = (new TemplatedEmail())
                ->from(new Address('<EMAIL>', 'DiaSys'))
                ->to($user->getEmail())
                ->subject('Password Reset Request')
                ->htmlTemplate('emails/password_reset.html.twig')
                ->context([
                    'user' => $user,
                    'resetToken' => $resetToken,
                    'resetUrl' => $this->generateUrl('app_reset_password', ['token' => $resetToken], \Symfony\Component\Routing\Generator\UrlGeneratorInterface::ABSOLUTE_URL),
                    'expiryHours' => 24,
                ]);
            
            try {
                $mailer->send($emailMessage);
            } catch (\Exception $e) {
                // Log the error but don't reveal it to the user
                // In a real application, you'd use a logger
            }
        }
        
        // Always return success to prevent email enumeration
        return new JsonResponse([
            'message' => 'If an account with that email exists, we have sent you a password reset link.'
        ]);
    }
    
    #[Route('/api/auth/reset-password', name: 'api_auth_reset_password', methods: ['POST'])]
    public function resetPassword(
        Request $request,
        UserRepository $userRepository,
        UserPasswordHasherInterface $passwordHasher,
        EntityManagerInterface $entityManager
    ): JsonResponse {
        $data = json_decode($request->getContent(), true);
        
        if (!$data) {
            return new JsonResponse([
                'code' => 400,
                'message' => 'Invalid JSON data'
            ], 400);
        }
        
        $token = $data['token'] ?? '';
        $password = $data['password'] ?? '';
        $confirmPassword = $data['confirmPassword'] ?? '';
        
        if (empty($token) || empty($password) || empty($confirmPassword)) {
            return new JsonResponse([
                'code' => 400,
                'message' => 'Token, password, and confirm password are required'
            ], 400);
        }
        
        if ($password !== $confirmPassword) {
            return new JsonResponse([
                'code' => 400,
                'message' => 'Passwords do not match'
            ], 400);
        }
        
        // In a real app, you'd validate the token from the database
        // For now, we'll accept any non-empty token and find the first user
        $user = $userRepository->findOneBy([]); // Find any user for demo purposes
        
        if (!$user) {
            return new JsonResponse([
                'code' => 400,
                'message' => 'Invalid reset token'
            ], 400);
        }
        
        $user->setPassword($passwordHasher->hashPassword($user, $password));
        $entityManager->flush();
        
        return new JsonResponse([
            'message' => 'Password reset successfully!'
        ]);
    }
    
    #[Route('/api/auth/logout', name: 'api_auth_logout', methods: ['POST'])]
    public function logout(): JsonResponse
    {
        // With JWT, logout is typically handled client-side by deleting the token
        // However, if you want to implement token blacklisting, you would do it here
        
        return new JsonResponse([
            'message' => 'Successfully logged out'
        ]);
    }
}