<?php

namespace App\Controller\Admin;

use App\Entity\User2FASetting;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ArrayField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;

class User2FASettingCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return User2FASetting::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('User 2FA Setting')
            ->setEntityLabelInPlural('User 2FA Settings')
            ->setDefaultSort(['id' => 'DESC']);
    }

    public function configureFields(string $pageName): iterable
    {
        yield AssociationField::new('user')->setLabel('User');
        yield TextField::new('secretKey')->setLabel('Secret Key');
        yield ArrayField::new('backupCodes')->setLabel('Backup Codes');
        yield DateTimeField::new('enabledAt')->setLabel('Enabled At');
        yield DateTimeField::new('lastUsedAt')->setLabel('Last Used At');
        yield ChoiceField::new('method')
            ->setLabel('Method')
            ->setChoices([
                'TOTP' => 'totp',
                'SMS' => 'sms',
                'Email' => 'email',
            ]);
        yield TextField::new('phoneNumber')->setLabel('Phone Number');
        yield DateTimeField::new('createdAt')->setLabel('Created At');
        yield DateTimeField::new('updatedAt')->setLabel('Updated At');
    }
}