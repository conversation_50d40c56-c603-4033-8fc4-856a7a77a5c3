<?php

namespace App\Controller\Admin;

use App\Entity\ConfigurationHistory;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextEditorField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;

class ConfigurationHistoryCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return ConfigurationHistory::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Configuration History')
            ->setEntityLabelInPlural('Configuration Histories')
            ->setSearchFields(['configKey'])
            ->setDefaultSort(['id' => 'DESC']);
    }

    public function configureFields(string $pageName): iterable
    {
        yield TextField::new('configKey')->setLabel('Config Key');
        yield TextEditorField::new('oldValue')->setLabel('Old Value');
        yield TextEditorField::new('newValue')->setLabel('New Value');
        yield AssociationField::new('changedBy')->setLabel('Changed By');
        yield TextEditorField::new('changeReason')->setLabel('Change Reason');
        yield TextField::new('ipAddress')->setLabel('IP Address');
        yield TextField::new('userAgent')->setLabel('User Agent');
        yield DateTimeField::new('timestamp')->setLabel('Timestamp');
        yield DateTimeField::new('createdAt')->setLabel('Created At');
    }
}