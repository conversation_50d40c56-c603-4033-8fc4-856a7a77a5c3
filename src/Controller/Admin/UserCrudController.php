<?php

namespace App\Controller\Admin;

use App\Entity\User;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\EmailField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ArrayField;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;

class UserCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return User::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('User')
            ->setEntityLabelInPlural('Users')
            ->setSearchFields(['email', 'phone', 'status'])
            ->setDefaultSort(['id' => 'DESC']);
    }

    public function configureFields(string $pageName): iterable
    {
        yield TextField::new('email')->setLabel('Email');
        yield TextField::new('phone')->setLabel('Phone');
        yield ChoiceField::new('status')
            ->setLabel('Status')
            ->setChoices([
                'Active' => 'active',
                'Pending' => 'pending',
                'Banned' => 'banned',
                'Disabled' => 'disabled',
                'Deleted' => 'deleted',
            ]);
        yield ArrayField::new('roles')->setLabel('Roles');
        yield DateTimeField::new('lastLoginAt')->setLabel('Last Login');
        yield AssociationField::new('profile')->setLabel('Profile');
        yield AssociationField::new('doctor')->setLabel('Doctor');
        yield AssociationField::new('patient')->setLabel('Patient');
        yield DateTimeField::new('createdAt')->setLabel('Created At');
        yield DateTimeField::new('updatedAt')->setLabel('Updated At');
    }
}