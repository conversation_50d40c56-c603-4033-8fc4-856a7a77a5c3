<?php

namespace App\Controller\Admin;

use App\Entity\SecurityEvent;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ArrayField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;

class SecurityEventCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return SecurityEvent::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Security Event')
            ->setEntityLabelInPlural('Security Events')
            ->setSearchFields(['eventType', 'category', 'severity'])
            ->setDefaultSort(['id' => 'DESC']);
    }

    public function configureFields(string $pageName): iterable
    {
        yield ChoiceField::new('eventType')
            ->setLabel('Event Type')
            ->setChoices([
                'Successful Login' => 'SUCCESSFUL_LOGIN',
                'Failed Login' => 'FAILED_LOGIN',
                'Suspicious Activity' => 'SUSPICIOUS_ACTIVITY',
                'Brute Force Attempt' => 'BRUTE_FORCE_ATTEMPT',
                'Unauthorized Access' => 'UNAUTHORIZED_ACCESS',
                'Data Exfiltration' => 'DATA_EXFILTRATION',
                'Malware Detected' => 'MALWARE_DETECTED',
                'Privilege Escalation' => 'PRIVILEGE_ESCALATION',
                'Account Takeover' => 'ACCOUNT_TAKEOVER',
                'Password Spray' => 'PASSWORD_SPRAY',
            ]);
        yield ChoiceField::new('category')
            ->setLabel('Category')
            ->setChoices([
                'Authentication' => 'AUTHENTICATION',
                'Authorization' => 'AUTHORIZATION',
                'Data Access' => 'DATA_ACCESS',
                'System Integrity' => 'SYSTEM_INTEGRITY',
                'Malware' => 'MALWARE',
            ]);
        yield ChoiceField::new('severity')
            ->setLabel('Severity')
            ->setChoices([
                'Low' => 'low',
                'Medium' => 'medium',
                'High' => 'high',
                'Critical' => 'critical',
            ]);
        yield AssociationField::new('user')->setLabel('User');
        yield TextField::new('ipAddress')->setLabel('IP Address');
        yield TextField::new('userAgent')->setLabel('User Agent');
        yield ArrayField::new('location')->setLabel('Location');
        yield ArrayField::new('details')->setLabel('Details');
        yield TextField::new('correlationId')->setLabel('Correlation ID');
        yield TextField::new('source')->setLabel('Source');
        yield BooleanField::new('isResolved')->setLabel('Is Resolved');
        yield DateTimeField::new('resolvedAt')->setLabel('Resolved At');
        yield AssociationField::new('resolvedBy')->setLabel('Resolved By');
        yield DateTimeField::new('timestamp')->setLabel('Timestamp');
        yield DateTimeField::new('createdAt')->setLabel('Created At');
        yield DateTimeField::new('updatedAt')->setLabel('Updated At');
    }
}