<?php

namespace App\Controller\Admin;

use App\Entity\UserNotification;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextEditorField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;

class UserNotificationCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return UserNotification::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('User Notification')
            ->setEntityLabelInPlural('User Notifications')
            ->setSearchFields(['title', 'message', 'type'])
            ->setDefaultSort(['id' => 'DESC']);
    }

    public function configureFields(string $pageName): iterable
    {
        yield AssociationField::new('user')->setLabel('User');
        yield TextField::new('title')->setLabel('Title');
        yield TextEditorField::new('message')->setLabel('Message');
        yield ChoiceField::new('type')
            ->setLabel('Type')
            ->setChoices([
                'Info' => 'info',
                'Warning' => 'warning',
                'Error' => 'error',
                'Success' => 'success',
                'System' => 'system',
            ]);
        yield ChoiceField::new('priority')
            ->setLabel('Priority')
            ->setChoices([
                'Low' => 'low',
                'Medium' => 'medium',
                'High' => 'high',
                'Urgent' => 'urgent',
            ]);
        yield ChoiceField::new('channel')
            ->setLabel('Channel')
            ->setChoices([
                'In App' => 'in_app',
                'Email' => 'email',
                'SMS' => 'sms',
                'Push' => 'push',
            ]);
        yield BooleanField::new('isRead')->setLabel('Is Read');
        yield DateTimeField::new('readAt')->setLabel('Read At');
        yield DateTimeField::new('sentAt')->setLabel('Sent At');
        yield DateTimeField::new('expiresAt')->setLabel('Expires At');
        yield TextField::new('actionUrl')->setLabel('Action URL');
        yield TextField::new('actionText')->setLabel('Action Text');
        yield AssociationField::new('createdBy')->setLabel('Created By');
        yield DateTimeField::new('createdAt')->setLabel('Created At');
        yield DateTimeField::new('updatedAt')->setLabel('Updated At');
    }
}