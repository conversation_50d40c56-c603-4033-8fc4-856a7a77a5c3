<?php

namespace App\Controller\Admin;

use App\Entity\UserVerification;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;

class UserVerificationCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return UserVerification::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('User Verification')
            ->setEntityLabelInPlural('User Verifications')
            ->setSearchFields(['verificationType', 'verificationMethod', 'token'])
            ->setDefaultSort(['id' => 'DESC']);
    }

    public function configureFields(string $pageName): iterable
    {
        yield AssociationField::new('user')->setLabel('User');
        yield ChoiceField::new('verificationType')
            ->setLabel('Verification Type')
            ->setChoices([
                'Email Verification' => 'email_verification',
                'Phone Verification' => 'phone_verification',
                '2FA Setup' => '2fa_setup',
                'Password Reset' => 'password_reset',
                'Password Change' => 'password_change',
                'Email Change' => 'email_change',
                'Phone Change' => 'phone_change',
            ]);
        yield ChoiceField::new('verificationMethod')
            ->setLabel('Verification Method')
            ->setChoices([
                'Email' => 'email',
                'Phone' => 'phone',
            ])
            ->setRequired(false);
        yield TextField::new('token')->setLabel('Token');
        yield DateTimeField::new('expiresAt')->setLabel('Expires At');
        yield DateTimeField::new('verifiedAt')->setLabel('Verified At');
        yield TextField::new('ipAddress')->setLabel('IP Address');
        yield TextField::new('userAgent')->setLabel('User Agent');
        yield DateTimeField::new('createdAt')->setLabel('Created At');
        yield DateTimeField::new('updatedAt')->setLabel('Updated At');
    }
}