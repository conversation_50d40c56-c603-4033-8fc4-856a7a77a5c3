<?php

namespace App\Controller\Admin;

use App\Entity\Role;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextEditorField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;

class RoleCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return Role::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Role')
            ->setEntityLabelInPlural('Roles')
            ->setSearchFields(['name', 'displayName'])
            ->setDefaultSort(['id' => 'DESC']);
    }

    public function configureFields(string $pageName): iterable
    {
        yield TextField::new('name')->setLabel('Name');
        yield TextField::new('displayName')->setLabel('Display Name');
        yield TextEditorField::new('description')->setLabel('Description');
        yield AssociationField::new('parent')->setLabel('Parent Role');
        yield BooleanField::new('isSystemRole')->setLabel('Is System Role');
        yield DateTimeField::new('createdAt')->setLabel('Created At');
        yield DateTimeField::new('updatedAt')->setLabel('Updated At');
    }
}