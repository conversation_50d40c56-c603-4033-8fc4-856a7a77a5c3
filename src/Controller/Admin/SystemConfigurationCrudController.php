<?php

namespace App\Controller\Admin;

use App\Entity\SystemConfiguration;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextEditorField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ArrayField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;

class SystemConfigurationCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return SystemConfiguration::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('System Configuration')
            ->setEntityLabelInPlural('System Configurations')
            ->setSearchFields(['configKey', 'category'])
            ->setDefaultSort(['id' => 'DESC']);
    }

    public function configureFields(string $pageName): iterable
    {
        yield TextField::new('configKey')->setLabel('Config Key');
        yield TextEditorField::new('configValue')->setLabel('Config Value');
        yield ChoiceField::new('configType')
            ->setLabel('Config Type')
            ->setChoices([
                'String' => 'string',
                'Integer' => 'integer',
                'Boolean' => 'boolean',
                'JSON' => 'json',
                'Array' => 'array',
            ]);
        yield TextField::new('category')->setLabel('Category');
        yield TextEditorField::new('description')->setLabel('Description');
        yield BooleanField::new('isSystemSetting')->setLabel('Is System Setting');
        yield BooleanField::new('isEditable')->setLabel('Is Editable');
        yield ArrayField::new('validationRules')->setLabel('Validation Rules')->hideOnIndex();
        yield TextEditorField::new('defaultValue')->setLabel('Default Value');
        yield DateTimeField::new('createdAt')->setLabel('Created At');
        yield DateTimeField::new('updatedAt')->setLabel('Updated At');
    }
}