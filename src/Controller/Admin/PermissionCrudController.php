<?php

namespace App\Controller\Admin;

use App\Entity\Permission;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextEditorField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;

class PermissionCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return Permission::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Permission')
            ->setEntityLabelInPlural('Permissions')
            ->setSearchFields(['name', 'displayName', 'resource'])
            ->setDefaultSort(['id' => 'DESC']);
    }

    public function configureFields(string $pageName): iterable
    {
        yield TextField::new('name')->setLabel('Name');
        yield TextField::new('displayName')->setLabel('Display Name');
        yield TextEditorField::new('description')->setLabel('Description');
        yield TextField::new('resource')->setLabel('Resource');
        yield ChoiceField::new('action')
            ->setLabel('Action')
            ->setChoices([
                'Create' => 'create',
                'Read' => 'read',
                'Update' => 'update',
                'Delete' => 'delete',
                'Execute' => 'execute',
            ]);
        yield BooleanField::new('isSystemPermission')->setLabel('Is System Permission');
        yield DateTimeField::new('createdAt')->setLabel('Created At');
        yield DateTimeField::new('updatedAt')->setLabel('Updated At');
    }
}