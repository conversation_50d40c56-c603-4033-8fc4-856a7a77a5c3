<?php

namespace App\Controller\Admin;

use App\Repository\DoctorRepository;
use App\Repository\PatientRepository;
use App\Repository\UserRepository;
use EasyCorp\Bundle\EasyAdminBundle\Attribute\AdminDashboard;
use EasyCorp\Bundle\EasyAdminBundle\Config\Dashboard;
use EasyCorp\Bundle\EasyAdminBundle\Config\MenuItem;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractDashboardController;
use Symfony\Component\HttpFoundation\Response;

#[AdminDashboard(routePath: '/admin/dashboard/', routeName: 'admin')]
class DashboardController extends AbstractDashboardController
{
    public function __construct(
        private UserRepository $userRepository,
        private DoctorRepository $doctorRepository,
        private PatientRepository $patientRepository
    ) {
    }

    public function configureDashboard(): Dashboard
    {
        return Dashboard::new()
            ->setTitle('DiaSys Admin')
            ->setFaviconPath('favicon.ico')
            ->renderContentMaximized()
            ->setDefaultColorScheme('auto'); // Supports both light and dark modes
    }

    public function index(): Response
    {
        // Fetch basic metrics
        $totalUsers = $this->userRepository->count([]);
        $totalDoctors = $this->doctorRepository->count([]);
        $totalPatients = $this->patientRepository->count([]);

        // Calculate additional metrics
        $today = new \DateTime('today');
        $todayAppointments = 0; // Would need appointment entity/repository
        $pendingAppointments = 0; // Would need appointment entity/repository
        $totalRevenue = rand(50000, 150000); // Mock data for demo
        $totalTests = rand(100, 500); // Mock data for demo
        $criticalResults = rand(0, 10); // Mock data for demo

        // Create enhanced metrics array
        $metrics = [
            'total_users' => $totalUsers,
            'total_doctors' => $totalDoctors,
            'total_patients' => $totalPatients,
            'total_revenue' => $totalRevenue,
            'today_appointments' => $todayAppointments,
            'pending_appointments' => $pendingAppointments,
            'total_tests' => $totalTests,
            'critical_results' => $criticalResults,
            'top_doctors' => [], // Could be populated with actual top doctors
            'system_health' => [
                'overall' => 'healthy',
                'database' => [
                    'status' => 'healthy',
                    'message' => 'Connected and responsive'
                ],
                'disk_space' => [
                    'status' => 'healthy',
                    'message' => 'Sufficient disk space available'
                ],
                'memory' => [
                    'status' => 'healthy',
                    'message' => 'Memory usage within normal limits'
                ],
                'cache' => [
                    'status' => 'healthy',
                    'message' => 'Cache functioning properly'
                ]
            ]
        ];

        return $this->render('admin/dashboard.html.twig', [
            'metrics' => $metrics
        ]);
    }

    public function configureMenuItems(): iterable
    {
        yield MenuItem::linkToDashboard('Dashboard', 'fa fa-home');
        
        // User Management Section
        yield MenuItem::section('User Management');
        yield MenuItem::linkToCrud('Users', 'fa fa-users', \App\Entity\User::class);
        yield MenuItem::linkToCrud('User Profiles', 'fa fa-address-card', \App\Entity\UserProfile::class);
        yield MenuItem::linkToCrud('User Verifications', 'fa fa-check-circle', \App\Entity\UserVerification::class);
        yield MenuItem::linkToCrud('2FA Settings', 'fa fa-lock', \App\Entity\User2FASetting::class);
        
        // Healthcare Providers Section
        yield MenuItem::section('Healthcare Providers');
        yield MenuItem::linkToCrud('Doctors', 'fa fa-user-md', \App\Entity\Doctor::class);
        yield MenuItem::linkToCrud('Patients', 'fa fa-hospital-user', \App\Entity\Patient::class);
        
        // Security Section
        yield MenuItem::section('Security & Access');
        yield MenuItem::linkToCrud('Roles', 'fa fa-user-tag', \App\Entity\Role::class);
        yield MenuItem::linkToCrud('Permissions', 'fa fa-key', \App\Entity\Permission::class);
        yield MenuItem::linkToCrud('User Roles', 'fa fa-users-cog', \App\Entity\UserRole::class);
        yield MenuItem::linkToCrud('Role Permissions', 'fa fa-user-shield', \App\Entity\RolePermission::class);
        
        // Audit & Monitoring Section
        yield MenuItem::section('Audit & Monitoring');
        yield MenuItem::linkToCrud('Audit Logs', 'fa fa-clipboard-list', \App\Entity\AuditLog::class);
        yield MenuItem::linkToCrud('Security Events', 'fa fa-shield-alt', \App\Entity\SecurityEvent::class);
        
        // System Configuration Section
        yield MenuItem::section('System Configuration');
        yield MenuItem::linkToCrud('System Configuration', 'fa fa-cogs', \App\Entity\SystemConfiguration::class);
        yield MenuItem::linkToCrud('Configuration History', 'fa fa-history', \App\Entity\ConfigurationHistory::class);
        
        // Communication Section
        yield MenuItem::section('Communication');
        yield MenuItem::linkToCrud('User Notifications', 'fa fa-bell', \App\Entity\UserNotification::class);
    }
}