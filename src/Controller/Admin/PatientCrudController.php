<?php

namespace App\Controller\Admin;

use App\Entity\Patient;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateField;
use EasyCorp\Bundle\EasyAdminBundle\Field\NumberField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ArrayField;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;

class PatientCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return Patient::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Patient')
            ->setEntityLabelInPlural('Patients')
            ->setSearchFields(['patientId', 'firstName', 'lastName', 'phone', 'email'])
            ->setDefaultSort(['id' => 'DESC']);
    }

    public function configureFields(string $pageName): iterable
    {
        yield TextField::new('patientId')->setLabel('Patient ID');
        yield TextField::new('firstName')->setLabel('First Name');
        yield TextField::new('lastName')->setLabel('Last Name');
        yield DateField::new('dateOfBirth')->setLabel('Date of Birth');
        yield ChoiceField::new('gender')
            ->setLabel('Gender')
            ->setChoices([
                'Male' => 'male',
                'Female' => 'female',
                'Other' => 'other',
                'Prefer not to say' => 'prefer_not_to_say',
            ]);
        yield TextField::new('phone')->setLabel('Phone');
        yield TextField::new('email')->setLabel('Email');
        yield ArrayField::new('address')->setLabel('Address');
        yield TextField::new('district')->setLabel('District');
        yield TextField::new('village')->setLabel('Village');
        yield TextField::new('postalCode')->setLabel('Postal Code');
        yield TextField::new('emergencyContactName')->setLabel('Emergency Contact Name');
        yield TextField::new('emergencyContactPhone')->setLabel('Emergency Contact Phone');
        yield TextField::new('bloodType')->setLabel('Blood Type');
        yield NumberField::new('height')->setLabel('Height (cm)');
        yield NumberField::new('weight')->setLabel('Weight (kg)');
        yield AssociationField::new('user')->setLabel('User');
        yield DateTimeField::new('createdAt')->setLabel('Created At');
        yield DateTimeField::new('updatedAt')->setLabel('Updated At');
    }
}