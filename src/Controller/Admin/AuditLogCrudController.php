<?php

namespace App\Controller\Admin;

use App\Entity\AuditLog;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ArrayField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;

class AuditLogCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return AuditLog::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Audit Log')
            ->setEntityLabelInPlural('Audit Logs')
            ->setSearchFields(['action', 'category', 'resourceType'])
            ->setDefaultSort(['id' => 'DESC']);
    }

    public function configureFields(string $pageName): iterable
    {
        yield AssociationField::new('user')->setLabel('User');
        yield ChoiceField::new('action')
            ->setLabel('Action')
            ->setChoices([
                'Create' => 'CREATE',
                'Read' => 'READ',
                'Update' => 'UPDATE',
                'Delete' => 'DELETE',
                'Login' => 'LOGIN',
                'Logout' => 'LOGOUT',
                'Failed Login' => 'FAILED_LOGIN',
                'Password Change' => 'PASSWORD_CHANGE',
                'Profile Update' => 'PROFILE_UPDATE',
                'Permission Grant' => 'PERMISSION_GRANT',
                'Permission Revoke' => 'PERMISSION_REVOKE',
                'Role Assign' => 'ROLE_ASSIGN',
                'Role Unassign' => 'ROLE_UNASSIGN',
            ]);
        yield ChoiceField::new('category')
            ->setLabel('Category')
            ->setChoices([
                'Authentication' => 'AUTHENTICATION',
                'User Management' => 'USER_MANAGEMENT',
                'Data Management' => 'DATA_MANAGEMENT',
                'System Configuration' => 'SYSTEM_CONFIGURATION',
                'Permission Management' => 'PERMISSION_MANAGEMENT',
            ]);
        yield TextField::new('resourceType')->setLabel('Resource Type');
        // Use a custom field that can handle any data type for resourceId
        yield TextField::new('resourceId')->setLabel('Resource ID')
            ->formatValue(function ($value, $entity) {
                // Handle different possible values and ensure string conversion
                try {
                    if ($value === null) {
                        return 'N/A';
                    }
                    if (is_array($value)) {
                        return json_encode($value, JSON_UNESCAPED_UNICODE);
                    }
                    // Force conversion to string, handling any unexpected types
                    return (string)$value;
                } catch (\Throwable $e) {
                    // If any error occurs during conversion, return a safe representation
                    return '[' . gettype($value) . ' value]';
                }
            })
            ->setFormType(\Symfony\Component\Form\Extension\Core\Type\TextType::class)
            ->setFormTypeOptions([
                'attr' => [
                    'readonly' => true,
                ],
            ])
            ->onlyOnDetail();  // Only show on detail page to avoid form issues
        yield ArrayField::new('oldValues')->setLabel('Old Values')
            ->formatValue(function ($value, $entity) {
                try {
                    return $value !== null ? json_encode($value, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) : 'N/A';
                } catch (\Throwable $e) {
                    return '[' . gettype($value) . ' value]';
                }
            })
            ->setFormType(\Symfony\Component\Form\Extension\Core\Type\TextareaType::class)
            ->setFormTypeOptions([
                'attr' => [
                    'readonly' => true,
                    'rows' => 5,
                ],
            ])
            ->hideOnIndex()
            ->hideOnForm();
        yield ArrayField::new('newValues')->setLabel('New Values')
            ->formatValue(function ($value, $entity) {
                try {
                    return $value !== null ? json_encode($value, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) : 'N/A';
                } catch (\Throwable $e) {
                    return '[' . gettype($value) . ' value]';
                }
            })
            ->setFormType(\Symfony\Component\Form\Extension\Core\Type\TextareaType::class)
            ->setFormTypeOptions([
                'attr' => [
                    'readonly' => true,
                    'rows' => 5,
                ],
            ])
            ->hideOnIndex()
            ->hideOnForm();
        yield TextField::new('ipAddress')->setLabel('IP Address');
        yield TextField::new('userAgent')->setLabel('User Agent');
        yield TextField::new('sessionId')->setLabel('Session ID');
        yield TextField::new('source')->setLabel('Source');
        yield BooleanField::new('success')->setLabel('Success');
        yield DateTimeField::new('timestamp')->setLabel('Timestamp');
        yield DateTimeField::new('createdAt')->setLabel('Created At');
    }
}