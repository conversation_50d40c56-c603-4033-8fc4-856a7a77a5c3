<?php

namespace App\Controller\Admin;

use App\Entity\RolePermission;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;

class RolePermissionCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return RolePermission::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Role Permission')
            ->setEntityLabelInPlural('Role Permissions')
            ->setDefaultSort(['id' => 'DESC']);
    }

    public function configureFields(string $pageName): iterable
    {
        yield AssociationField::new('role')->setLabel('Role');
        yield AssociationField::new('permission')->setLabel('Permission');
        yield AssociationField::new('grantedBy')->setLabel('Granted By');
        yield DateTimeField::new('grantedAt')->setLabel('Granted At');
        yield DateTimeField::new('createdAt')->setLabel('Created At');
        yield DateTimeField::new('updatedAt')->setLabel('Updated At');
    }
}