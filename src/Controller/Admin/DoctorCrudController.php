<?php

namespace App\Controller\Admin;

use App\Entity\Doctor;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateField;
use EasyCorp\Bundle\EasyAdminBundle\Field\NumberField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextEditorField;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ArrayField;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;

class DoctorCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return Doctor::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Doctor')
            ->setEntityLabelInPlural('Doctors')
            ->setSearchFields(['firstName', 'lastName', 'registrationNumber', 'specialization'])
            ->setDefaultSort(['id' => 'DESC']);
    }

    public function configureFields(string $pageName): iterable
    {
        yield TextField::new('prefix')->setLabel('Prefix');
        yield TextField::new('firstName')->setLabel('First Name');
        yield TextField::new('lastName')->setLabel('Last Name');
        yield TextField::new('suffix')->setLabel('Suffix');
        yield TextField::new('registrationNumber')->setLabel('Registration Number');
        yield DateField::new('registrationExpiryDate')->setLabel('Registration Expiry Date');
        yield ArrayField::new('education')->setLabel('Education')->hideOnIndex();
        yield TextField::new('degree')->setLabel('Degree');
        yield TextField::new('specialization')->setLabel('Specialization');
        yield NumberField::new('yearsOfExperience')->setLabel('Years of Experience');
        yield TextEditorField::new('bio')->setLabel('Biography');
        yield TextField::new('picture')->setLabel('Picture');
        yield NumberField::new('consultationFee')->setLabel('Consultation Fee');
        yield ArrayField::new('availabilitySchedule')->setLabel('Availability Schedule')->hideOnIndex();
        yield ChoiceField::new('status')
            ->setLabel('Status')
            ->setChoices([
                'Active' => 'active',
                'Inactive' => 'inactive',
            ]);
        yield AssociationField::new('user')->setLabel('User');
        yield DateTimeField::new('createdAt')->setLabel('Created At');
        yield DateTimeField::new('updatedAt')->setLabel('Updated At');
    }
}