# Redesigned Healthcare Database Schema

**Version**: 3.17 | **Last Updated**: 2025-09-04 | **Status**: Production Ready

## Table of Contents
- [Overview](#overview)
- [Authentication and User Management](#authentication-and-user-management)
  - [1. Users Table](#1-users-table)
  - [2. User Profiles Table](#2-user-profiles-table)
  - [3. User Verifications Table](#3-user-verifications-table)
- [Healthcare Provider Management](#healthcare-provider-management)
  - [4. Doctors Table](#4-doctors-table)
- [Patient Management](#patient-management)
 - [5. Patients Table](#5-patients-table)
- [Security and Access Control](#security-and-access-control)
  - [6. Roles Table](#6-roles-table)
  - [7. Permissions Table](#7-permissions-table)
  - [8. User Roles Table](#8-user-roles-table)
  - [9. Role Permissions Table](#9-role-permissions-table)
  - [10. User 2FA Settings Table](#10-user-2fa-settings-table)
- [Audit and Monitoring](#audit-and-monitoring)
  - [11. Audit Log Table](#11-audit-log-table)
  - [12. Security Events Table](#12-security-events-table)
- [System Management](#system-management)
  - [13. System Configuration Table](#13-system-configuration-table)
  - [14. Configuration History Table](#14-configuration-history-table)
- [Communication](#communication)
 - [15. User Notifications Table](#15-user-notifications-table)

## Overview

This document outlines the redesigned database structure for a healthcare system with detailed table schemas in markdown format. The schema supports a comprehensive healthcare management system with robust security, audit capabilities, and regulatory compliance features.

The database design follows these principles:
- **Data Integrity**: Foreign key constraints and check constraints ensure data consistency
- **Security**: Comprehensive audit logging, encryption requirements, and access controls
- **Scalability**: Proper indexing and partitioning strategies for performance
- **Compliance**: Designed to meet healthcare data protection regulations (HIPAA, GDPR)
- **Extensibility**: Modular design allowing for future enhancements

---

## Authentication and User Management

### 1. Users Table

**Purpose**: Central authentication and user management table storing core user credentials and basic profile linking information.

| Column Name | Data Type | Constraints | Description |
|-------------|-------------|-------------|
| id | BIGSERIAL | PRIMARY KEY | Unique identifier for the user |
| email | VARCHAR | NOT NULL, UNIQUE | User's primary email address for authentication and communication |
| phone | VARCHAR | NULL, UNIQUE | User's mobile phone number with country code |
| password | VARCHAR | NOT NULL | Securely hashed password using bcrypt algorithm |
| roles | JSON | NOT NULL, DEFAULT '[]' | Array of user roles in JSON format for authorization |
| status | VARCHAR | NOT NULL, DEFAULT 'pending' | Current account status affecting login access. Valid values: 'active', 'pending', 'banned', 'disabled', 'deleted' |
| last_login_at | TIMESTAMP | NULL | Timestamp of user's most recent successful login |
| profile_id | INTEGER | NULL, REFERENCES user_profiles(id) ON DELETE SET NULL | Links to extended user profile information |
| doctor_id | INTEGER | NULL, REFERENCES doctors(id) ON DELETE SET NULL | Links to doctor profile if user is a healthcare provider |
| patient_id | INTEGER | NULL, REFERENCES patients(id) ON DELETE SET NULL | Links to patient profile if user is a patient |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Account creation timestamp |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Last account modification timestamp |

**Indexes:**
- email
- phone
- status
- profile_id
- doctor_id
- patient_id

### 2. User Profiles Table

**Purpose**: Stores extended profile information for users that is not required for authentication.

| Column Name | Data Type | Constraints | Description |
|-------------|-----------|-------------|-------------|
| id | BIGSERIAL | PRIMARY KEY | Unique identifier for the extended profile |
| first_name | VARCHAR | NOT NULL | User's legal first name as per identification |
| last_name | VARCHAR | NOT NULL | User's legal last name/family name |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Profile creation timestamp |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Last profile modification timestamp |

**Indexes:**
- Combined last_name, first_name to name

---

### 3. User Verifications Table

**Purpose**: Stores verification tokens for various user actions including email, phone, and other verification processes.

| Column Name | Data Type | Constraints | Description |
|-------------|-----------|-------------|-------------|
| id | BIGSERIAL | PRIMARY KEY | Unique identifier for the verification record |
| user_id | INTEGER | NOT NULL, REFERENCES users(id) ON DELETE CASCADE | User associated with this verification |
| verification_type | VARCHAR | NOT NULL | Type of verification. Valid values: 'email_verification', 'phone_verification', '2fa_setup', 'password_reset', 'password_change', 'email_change', 'phone_change' |
| verification_method | VARCHAR | NULL | Method chosen for verification. Valid values: 'email', 'phone' |
| token | VARCHAR | NOT NULL, UNIQUE | Secure token for verification process |
| expires_at | TIMESTAMP | NOT NULL | When the verification token expires |
| verified_at | TIMESTAMP | NULL | When the verification was completed (NULL = not verified) |
| ip_address | INET | NULL | IP address of the verification request |
| user_agent | TEXT | NULL | User agent of the verification request |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record update timestamp |

**Indexes:**
- user_id
- verification_type
- verification_method
- token
- expires_at
- verified_at
- ip_address

---

## Healthcare Provider Management

### 4. Doctors Table

**Purpose**: Stores professional information for healthcare providers in the system.

| Column Name | Data Type | Constraints | Description |
|-------------|-----------|-------------|-------------|
| id | BIGSERIAL | PRIMARY KEY | Unique identifier for the healthcare provider |
| prefix | VARCHAR | NULL | Professional title (Dr., Prof., etc.). Valid values: 'Dr.', 'Mr.', 'Ms.', 'Mrs.', 'Prof.', 'Rev.' |
| first_name | VARCHAR | NOT NULL | Doctor's legal first name |
| last_name | VARCHAR | NOT NULL | Doctor's legal last name/family name |
| suffix | VARCHAR | NULL | Professional credentials (MD, PhD, DO, MBBS) |
| registration_number | VARCHAR | NOT NULL, UNIQUE | Official medical license/registration number |
| registration_expiry_date | DATE | NULL | License expiration date for renewal tracking |
| education | JSON | NULL | Complete educational background with institutions, degrees, and graduation years |
| degree | VARCHAR | NOT NULL | Primary medical qualification (MD, DO, MBBS, etc.) |
| specialization | VARCHAR | NOT NULL | Medical specialty (Cardiology, Pediatrics, etc.) |
| years_of_experience | INTEGER | NULL, CHECK (years_of_experience >= 0) | Total years of clinical practice experience |
| bio | TEXT | NULL | Professional biography and background information |
| picture | VARCHAR | NULL | File path to doctor's profile photograph |
| consultation_fee | DECIMAL | NULL, CHECK (consultation_fee >= 0) | Standard consultation fee in local currency |
| availability_schedule | JSON | NULL | Weekly availability schedule with time slots |
| status | VARCHAR | NOT NULL, DEFAULT 'active' | Current professional status and availability. Valid values: 'active', 'inactive' |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record update timestamp |

**Indexes:**
- Combined last_name, first_name to name
- registration_number
- specialization
- degree
- consultation_fee
- status
- education

---

## Patient Management

### 5. Patients Table

**Purpose**: Stores comprehensive patient information for healthcare delivery and record keeping.

| Column Name | Data Type | Constraints | Description |
|-------------|-----------|-------------|-------------|
| id | BIGSERIAL | PRIMARY KEY | Unique identifier for the patient record |
| patient_id | VARCHAR | NOT NULL, UNIQUE | Auto-generated patient ID (format: P<YY><WEEK><LCGNUMBER>) |
| first_name | VARCHAR | NOT NULL | Patient's legal first name |
| last_name | VARCHAR | NOT NULL | Patient's legal last name/family name |
| date_of_birth | DATE | NOT NULL | Patient's date of birth for age calculations |
| gender | VARCHAR | NOT NULL | Patient's gender identity. Valid values: 'male', 'female', 'other', 'prefer_not_to_say' |
| phone | VARCHAR | NOT NULL | Primary contact phone number with country code |
| email | VARCHAR | NULL | Patient's email address for communications |
| address | JSON | NULL | Complete address information including street, city, state |
| district | VARCHAR | NULL | Administrative district/region for location-based services |
| village | VARCHAR | NULL | Village/town/area name for precise location |
| postal_code | VARCHAR | NULL | Postal/ZIP code for mail delivery |
| emergency_contact_name | VARCHAR | NOT NULL | Full name of emergency contact person |
| emergency_contact_phone | VARCHAR | NOT NULL | Emergency contact's phone number |
| blood_type | VARCHAR | NULL | ABO blood group and Rh factor (A+, B-, etc.) |
| height | DECIMAL | NULL, CHECK (height > 0 AND height <= 300) | Patient's height in centimeters |
| weight | DECIMAL | NULL, CHECK (weight > 0 AND weight <= 1000) | Patient's weight in kilograms |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record update timestamp |

**Indexes:**
- Combined last_name, first_name to name
- patient_id
- district
- village

---

## Security and Access Control

### 6. Roles Table

**Purpose**: Defines security roles that can be assigned to users for access control.

| Column Name | Data Type | Constraints | Description |
|-------------|-----------|-------------|-------------|
| id | BIGSERIAL | PRIMARY KEY | Unique identifier for the security role |
| name | VARCHAR | NOT NULL, UNIQUE | Machine-readable role name (e.g., 'ROLE_ADMIN', 'ROLE_DOCTOR') |
| display_name | VARCHAR | NOT NULL | User-friendly role name for UI display |
| description | TEXT | NULL | Detailed explanation of role responsibilities and access level |
| parent_id | INTEGER | NULL, REFERENCES roles(id) ON DELETE SET NULL | Parent role for hierarchical inheritance (e.g., ADMIN inherits DOCTOR permissions) |
| is_system_role | BOOLEAN | NOT NULL, DEFAULT FALSE | Indicates if this is a core system role that cannot be deleted |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record update timestamp |

**Indexes:**
- name
- parent_id

### 7. Permissions Table

**Purpose**: Defines granular permissions that can be granted to roles for fine-grained access control.

| Column Name | Data Type | Constraints | Description |
|-------------|-----------|-------------|-------------|
| id | BIGSERIAL | PRIMARY KEY | Unique identifier for the granular permission |
| name | VARCHAR | NOT NULL, UNIQUE | Machine-readable permission name (e.g., 'PATIENT_READ', 'TEST_ORDER') |
| display_name | VARCHAR | NOT NULL | User-friendly permission name for administration UI |
| description | TEXT | NULL | Detailed explanation of what actions this permission enables |
| resource | VARCHAR | NOT NULL | Protected resource type (e.g., 'patient', 'test_result', 'invoice') |
| action | VARCHAR | NOT NULL | CRUD operation type. Valid values: 'create', 'read', 'update', 'delete', 'execute' |
| is_system_permission | BOOLEAN | NOT NULL, DEFAULT FALSE | Indicates core system permission that cannot be modified |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record update timestamp |

**Indexes:**
- name
- resource
- action

### 8. User Roles Table

**Purpose**: Junction table that associates users with their assigned roles.

| Column Name | Data Type | Constraints | Description |
|-------------|-----------|-------------|-------------|
| id | BIGSERIAL | PRIMARY KEY | Unique identifier for the user-role assignment |
| user_id | INTEGER | NOT NULL, REFERENCES users(id) ON DELETE CASCADE | User assigned to the role |
| role_id | INTEGER | NOT NULL, REFERENCES roles(id) ON DELETE CASCADE | Role assigned to the user |
| assigned_by | INTEGER | NOT NULL, REFERENCES users(id) ON DELETE RESTRICT | User who assigned this role |
| assigned_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | When the role was assigned |
| notes | TEXT | NULL | Additional notes about the assignment |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record update timestamp |

**Indexes:**
- user_id
- role_id
- assigned_by

**Unique Constraints:**
- UNIQUE(user_id, role_id) - Prevent duplicate role assignments for the same user

### 9. Role Permissions Table

**Purpose**: Junction table that associates roles with their granted permissions.

| Column Name | Data Type | Constraints | Description |
|-------------|-----------|-------------|-------------|
| id | BIGSERIAL | PRIMARY KEY | Unique identifier for the role-permission assignment |
| role_id | INTEGER | NOT NULL, REFERENCES roles(id) ON DELETE CASCADE | Role that has the permission |
| permission_id | INTEGER | NOT NULL, REFERENCES permissions(id) ON DELETE CASCADE | Permission granted to the role |
| granted_by | INTEGER | NOT NULL, REFERENCES users(id) ON DELETE RESTRICT | User who granted this permission |
| granted_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | When the permission was granted |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record update timestamp |

**Indexes:**
- role_id
- permission_id
- granted_by

### 10. User 2FA Settings Table

**Purpose**: Stores two-factor authentication settings for users who have enabled this security feature.

| Column Name | Data Type | Constraints | Description |
|-------------|-----------|-------------|-------------|
| id | BIGSERIAL | PRIMARY KEY | Unique identifier for 2FA settings |
| user_id | INTEGER | NOT NULL, UNIQUE, REFERENCES users(id) ON DELETE CASCADE | User with 2FA enabled |
| secret_key | VARCHAR | NOT NULL | TOTP secret key for 2FA |
| backup_codes | JSON | NULL | Backup recovery codes |
| enabled_at | TIMESTAMP | NULL | When 2FA was enabled (NULL = disabled) |
| last_used_at | TIMESTAMP | NULL | Last time 2FA was used |
| method | VARCHAR | NOT NULL, DEFAULT 'totp' | 2FA method used. Valid values: 'totp', 'sms', 'email' |
| phone_number | VARCHAR | NULL | Phone number for SMS 2FA |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record update timestamp |

**Indexes:**
- user_id
- enabled_at

---

## Audit and Monitoring

### 11. Audit Log Table

**Purpose**: Records all significant system activities for compliance, troubleshooting, and security monitoring.

| Column Name | Data Type | Constraints | Description |
|-------------|-----------|-------------|-------------|
| id | BIGSERIAL | PRIMARY KEY | Unique identifier for the audit entry |
| user_id | INTEGER | NULL, REFERENCES users(id) ON DELETE SET NULL | User who performed the action |
| action | VARCHAR | NOT NULL | Action performed. Valid values: 'CREATE', 'READ', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'FAILED_LOGIN', 'PASSWORD_CHANGE', 'PROFILE_UPDATE', 'PERMISSION_GRANT', 'PERMISSION_REVOKE', 'ROLE_ASSIGN', 'ROLE_UNASSIGN' |
| category | VARCHAR | NOT NULL | Category of the action. Valid values: 'AUTHENTICATION', 'USER_MANAGEMENT', 'DATA_MANAGEMENT', 'SYSTEM_CONFIGURATION', 'PERMISSION_MANAGEMENT' |
| resource_type | VARCHAR | NOT NULL | Type of resource affected (e.g., 'user', 'test') |
| resource_id | INTEGER | NULL | ID of the affected resource |
| old_values | JSON | NULL | Previous values before the change |
| new_values | JSON | NULL | New values after the change |
| ip_address | INET | NULL | IP address of the user |
| user_agent | TEXT | NULL | Browser/client information |
| session_id | VARCHAR | NULL | User session identifier |
| source | VARCHAR | NULL | Source of the action (e.g., 'WEB', 'API', 'MOBILE') |
| success | BOOLEAN | NOT NULL, DEFAULT TRUE | Whether the action was successful |
| timestamp | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | When the action occurred |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |

**Indexes:**
- user_id
- action
- category
- resource_type
- resource_id
- timestamp
- success

### 12. Security Events Table

**Purpose**: Tracks security-related events for monitoring and incident response.

| Column Name | Data Type | Constraints | Description |
|-------------|-----------|-------------|-------------|
| id | BIGSERIAL | PRIMARY KEY | Unique identifier for the security event |
| event_type | VARCHAR | NOT NULL | Type of security event. Valid values: 'SUCCESSFUL_LOGIN', 'FAILED_LOGIN', 'SUSPICIOUS_ACTIVITY', 'BRUTE_FORCE_ATTEMPT', 'UNAUTHORIZED_ACCESS', 'DATA_EXFILTRATION', 'MALWARE_DETECTED', 'PRIVILEGE_ESCALATION', 'ACCOUNT_TAKEOVER', 'PASSWORD_SPRAY' |
| category | VARCHAR | NOT NULL | Category of the security event. Valid values: 'AUTHENTICATION', 'AUTHORIZATION', 'DATA_ACCESS', 'SYSTEM_INTEGRITY', 'MALWARE' |
| severity | VARCHAR | NOT NULL, DEFAULT 'medium' | Severity level of the event. Valid values: 'low', 'medium', 'high', 'critical' |
| user_id | INTEGER | NULL, REFERENCES users(id) ON DELETE SET NULL | User associated with the event |
| ip_address | INET | NULL | IP address involved in the event |
| user_agent | TEXT | NULL | Browser/client information |
| location | JSON | NULL | Geographic location information |
| details | JSON | NULL | Additional event details |
| correlation_id | VARCHAR | NULL | Identifier to correlate related events |
| source | VARCHAR | NULL | Source of the event (e.g., 'WEB', 'API', 'MOBILE', 'SYSTEM') |
| is_resolved | BOOLEAN | NOT NULL, DEFAULT FALSE | Whether the security issue has been resolved |
| resolved_at | TIMESTAMP | NULL | When the issue was resolved |
| resolved_by | INTEGER | NULL, REFERENCES users(id) ON DELETE SET NULL | User who resolved the issue |
| timestamp | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | When the event occurred |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record update timestamp |

**Indexes:**
- event_type
- category
- severity
- user_id
- ip_address
- timestamp
- is_resolved
- correlation_id

---

## System Management

### 13. System Configuration Table

**Purpose**: Stores system-wide configuration settings that can be modified without code changes.

| Column Name | Data Type | Constraints | Description |
|-------------|-----------|-------------|-------------|
| id | BIGSERIAL | PRIMARY KEY | Unique identifier for the configuration entry |
| config_key | VARCHAR | NOT NULL, UNIQUE | Dot-notation key (e.g., 'app.timezone', 'email.smtp.host') |
| config_value | TEXT | NULL | Current configuration value (string, number, JSON, etc.) |
| config_type | VARCHAR | NOT NULL, DEFAULT 'string' | Data type for proper validation and parsing. Valid values: 'string', 'integer', 'boolean', 'json', 'array' |
| category | VARCHAR | NOT NULL | Logical grouping. Valid values: 'system', 'email', 'security', 'billing', 'laboratory' |
| description | TEXT | NULL | Human-readable explanation of the setting's purpose |
| is_system_setting | BOOLEAN | NOT NULL, DEFAULT FALSE | Core system setting that requires special permissions to modify |
| is_editable | BOOLEAN | NOT NULL, DEFAULT TRUE | Whether administrators can modify this setting via UI |
| validation_rules | JSON | NULL | JSON schema for value validation (min/max, patterns, etc.) |
| default_value | TEXT | NULL | Fallback value if setting is not configured |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record update timestamp |

**Indexes:**
- config_key
- category
- is_system_setting

### 14. Configuration History Table

**Purpose**: Tracks changes to system configuration for audit and rollback purposes.

| Column Name | Data Type | Constraints | Description |
|-------------|-------------|-------------|-------------|
| id | BIGSERIAL | PRIMARY KEY | Unique identifier for the configuration change |
| config_key | VARCHAR | NOT NULL | Configuration key that was changed |
| old_value | TEXT | NULL | Previous configuration value |
| new_value | TEXT | NULL | New configuration value |
| changed_by | INTEGER | NOT NULL, REFERENCES users(id) | User who made the change |
| change_reason | TEXT | NULL | Reason for the configuration change |
| ip_address | INET | NULL | IP address of the user making the change |
| user_agent | TEXT | NULL | Browser/client information |
| timestamp | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | When the change occurred |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |

**Indexes:**
- config_key
- changed_by
- timestamp

---

## Communication

### 15. User Notifications Table

**Purpose**: Stores notifications sent to users through various channels.

| Column Name | Data Type | Constraints | Description |
|-------------|-----------|-------------|-------------|
| id | BIGSERIAL | PRIMARY KEY | Unique identifier for the notification |
| user_id | INTEGER | NOT NULL, REFERENCES users(id) ON DELETE CASCADE | User receiving the notification |
| title | VARCHAR | NOT NULL | Notification title/headline |
| message | TEXT | NOT NULL | Notification content/message |
| type | VARCHAR | NOT NULL, DEFAULT 'info' | Notification type/category. Valid values: 'info', 'warning', 'error', 'success', 'system' |
| priority | VARCHAR | NOT NULL, DEFAULT 'medium' | Notification priority level. Valid values: 'low', 'medium', 'high', 'urgent' |
| channel | VARCHAR | NOT NULL, DEFAULT 'in_app' | Delivery channel/method. Valid values: 'in_app', 'email', 'sms', 'push' |
| is_read | BOOLEAN | NOT NULL, DEFAULT FALSE | Whether the user has read the notification |
| read_at | TIMESTAMP | NULL | When the notification was read |
| sent_at | TIMESTAMP | NULL | When the notification was sent/delivered |
| expires_at | TIMESTAMP | NULL | When the notification expires |
| action_url | VARCHAR | NULL | URL for notification action button |
| action_text | VARCHAR | NULL | Text for notification action button |
| metadata | JSON | NULL | Additional notification data/metadata |
| created_by | INTEGER | NULL, REFERENCES users(id) ON DELETE SET NULL | User/system that created the notification |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record update timestamp |

**Indexes:**
- user_id
- type
- priority
- channel
- is_read
- sent_at
- expires_at
- created_at

---

## Relationship Diagram

```mermaid
erDiagram
    USERS ||--o{ USER_PROFILES : has
    USERS ||--o{ USER_VERIFICATIONS : has
    USERS ||--o{ DOCTORS : "can be"
    USERS ||--o{ PATIENTS : "can be"
    USERS ||--o{ USER_ROLES : assigned
    USERS ||--o{ ROLE_PERMISSIONS : granted
    USERS ||--|| USER_2FA_SETTINGS : has
    USERS ||--o{ AUDIT_LOG : performs
    USERS ||--o{ SECURITY_EVENTS : associated
    USERS ||--o{ CONFIGURATION_HISTORY : changes
    USERS ||--o{ USER_NOTIFICATIONS : receives
    
    ROLES ||--o{ USER_ROLES : assigned
    ROLES ||--o{ ROLE_PERMISSIONS : granted
    ROLES ||--o{ ROLES : inherits
    
    PERMISSIONS ||--o{ ROLE_PERMISSIONS : granted
    
    DOCTORS ||--o{ PATIENTS : treats
    
    CONFIGURATION ||--o{ CONFIGURATION_HISTORY : tracks
```

## Security Considerations

1. **Data Encryption**: Sensitive data such as passwords, 2FA secrets, and PII should be encrypted at rest
2. **Access Controls**: Role-based access control (RBAC) limits data access based on user roles
3. **Audit Logging**: All data modifications and access attempts are logged for compliance
4. **Data Retention**: Healthcare data retention policies must comply with local regulations
5. **Backup and Recovery**: Regular backups with secure storage and tested recovery procedures
6. **Network Security**: Database access should be restricted to application servers only
7. **Vulnerability Management**: Regular security assessments and patch management

## Performance Recommendations

1. **Indexing**: Critical columns are indexed for query performance
2. **Partitioning**: Large tables should be partitioned by date for better performance
3. **Connection Pooling**: Use connection pooling to manage database connections efficiently
4. **Query Optimization**: Regular query performance analysis and optimization
5. **Caching**: Implement appropriate caching strategies for frequently accessed data
6. **Monitoring**: Continuous monitoring of database performance metrics
7. **Maintenance**: Regular database maintenance including statistics updates and index rebuilds
