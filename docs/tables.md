## 📋 **Healthcare Database Tables Overview**

### 🏥 **Core Entities** ✅
| Table Name | Description |
|------------|-------------|
| users | User authentication and basic profile information |
| doctors | Healthcare provider information and credentials |
| patients | Patient demographic and medical record data |

### 🏥 **Extended Core Entities** ✅
| Table Name | Description |
|------------|-------------|
| user_profiles | Extended user profile details and preferences |

### 🔐 **Security** ✅
| Table Name | Description |
|------------|-------------|
| roles | User role definitions and hierarchies |
| permissions | Specific system access rights |
| user_roles | Assignment of roles to users |
| role_permissions | Linking roles to specific permissions |
| user_password_resets | Secure password recovery and token management |
| user_2fa_settings | Two-factor authentication configuration and secrets |
| audit_log | System activity tracking and compliance |
| security_events | Authentication logs, security incidents and threat monitoring |

### ⚙️ **System Administration** ✅
| Table Name | Description |
|------------|-------------|
| system_configuration | Application settings and parameters |
| configuration_history | Change tracking for system settings |
| user_notifications | User notification messages and delivery tracking |

### 📅 **Patient Management**
| Table Name | Description |
|------------|-------------|
| appointments | Scheduled patient-doctor consultations |
| appointment_statuses | Standardized appointment status options and definitions |
| appointment_cancellation_reasons | Cancellation reason codes and descriptions |
| doctor_schedules | Doctor availability and working hours |
| doctor_patient_relationships | Ongoing care relationships between doctors and patients |

### 🧪 **Laboratory Operations**
| Table Name | Description |
|------------|-------------|
| diagnostic_services | Diagnostic services offered by the laboratory |
| diagnostic_service_categories | Classification of diagnostic services and departments |
| lab_tests | Laboratory test definitions and specifications |
| test_orders | Patient test requests and ordering details |
| test_results | Laboratory test outcomes and measurements |
| test_result_verifications | Quality control and pathologist result validation |
| test_protocols | Standardized testing procedures and methodologies |
| test_categories | Grouping of related laboratory test types |
| test_parameters | Individual test measurements and biomarkers |
| test_reference_ranges | Normal and abnormal value ranges for tests |
| lab_analytics | Laboratory performance and quality metrics |
| specimens | Biological sample collection and tracking |
| lab_equipment | Laboratory equipment and instrument inventory |
| quality_controls | Quality control standards and calibration records |

### 💰 **Financial Management**
| Table Name | Description |
|------------|-------------|
| invoices | Billing statements for services rendered |
| invoice_items | Detailed line items within invoices |
| payment_methods | Standardized payment method options and configurations |
| payments_transactions | Customer payment transactions and processing |
| receipts | Payment confirmations and documentation |
| tax_rates | Tax rate configurations by region and service type |
| discounts | Discount offers and promotional pricing |

### 📊 **Analytics & Insights**
| Table Name | Description |
|------------|-------------|
| medical_history | Patient medical records and treatment history |
| financial_reports | Revenue and financial performance analysis |
| doctor_performance | Provider productivity and quality metrics |

### 🔧 **Infrastructure & Equipment**
| Table Name | Description |
|------------|-------------|
| devices | Medical equipment inventory and status |

---

**Total: 44 tables** | **Categories: 8**

## 🔗 **Refactor Links**
- **[Database Migration Scripts](migrations/)** - Implementation scripts for schema changes
- **[Entity Relationships Diagram](docs/entity-relations.md)** - Visual representation of table relationships
- **[API Documentation](docs/api-endpoints.md)** - REST API endpoints for each table
- **[Data Flow Documentation](docs/data-flow.md)** - Data flow between tables and modules

---

**Last Updated**: 2025-09-04 | **Version**: 3.0 | **Status**: Production Ready