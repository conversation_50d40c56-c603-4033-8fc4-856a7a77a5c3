#!/usr/bin/env php
<?php

/**
 * Log Analysis Script for Symfony Application
 *
 * This script analyzes log files and provides insights into:
 * - Error patterns
 * - Performance metrics
 * - Security events
 * - Database issues
 */

declare(strict_types=1);

class LogAnalyzer
{
    private string $logsDir;
    private array $stats = [];

    public function __construct(string $logsDir = null)
    {
        $this->logsDir = $logsDir ?? __DIR__ . '/../../var/log';
        $this->stats = [
            'total_lines' => 0,
            'errors' => 0,
            'warnings' => 0,
            'critical' => 0,
            'security_events' => 0,
            'database_errors' => 0,
            'performance_issues' => 0,
            'files_analyzed' => 0,
        ];
    }

    public function analyze(): array
    {
        $logFiles = $this->findLogFiles();

        foreach ($logFiles as $file) {
            $this->analyzeFile($file);
        }

        return $this->stats;
    }

    private function findLogFiles(): array
    {
        $files = [];

        if (!is_dir($this->logsDir)) {
            echo "Logs directory not found: {$this->logsDir}\n";
            return $files;
        }

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($this->logsDir)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'log') {
                $files[] = $file->getPathname();
            }
        }

        return $files;
    }

    private function analyzeFile(string $filePath): void
    {
        $this->stats['files_analyzed']++;

        $handle = fopen($filePath, 'r');
        if (!$handle) {
            echo "Cannot open file: {$filePath}\n";
            return;
        }

        while (($line = fgets($handle)) !== false) {
            $this->stats['total_lines']++;
            $this->analyzeLine($line);
        }

        fclose($handle);
    }

    private function analyzeLine(string $line): void
    {
        $line = strtolower($line);

        // Error patterns
        if (preg_match('/\b(error|exception|fatal)\b/i', $line)) {
            $this->stats['errors']++;
        }

        // Warning patterns
        if (preg_match('/\b(warning|notice)\b/i', $line)) {
            $this->stats['warnings']++;
        }

        // Critical patterns
        if (preg_match('/\b(critical|emergency|alert)\b/i', $line)) {
            $this->stats['critical']++;
        }

        // Security events
        if (preg_match('/\b(security|auth|login|access|denied)\b/i', $line)) {
            $this->stats['security_events']++;
        }

        // Database errors
        if (preg_match('/\b(database|pdo|sql|query)\b.*\b(error|failed|exception)\b/i', $line)) {
            $this->stats['database_errors']++;
        }

        // Performance issues
        if (preg_match('/\b(timeout|slow|performance|memory|cpu)\b/i', $line)) {
            $this->stats['performance_issues']++;
        }
    }

    public function generateReport(): string
    {
        $report = "=== Log Analysis Report ===\n";
        $report .= "Generated: " . date('Y-m-d H:i:s') . "\n";
        $report .= "Logs Directory: {$this->logsDir}\n\n";

        $report .= "Summary:\n";
        $report .= "- Total lines analyzed: {$this->stats['total_lines']}\n";
        $report .= "- Files analyzed: {$this->stats['files_analyzed']}\n";
        $report .= "- Errors found: {$this->stats['errors']}\n";
        $report .= "- Warnings found: {$this->stats['warnings']}\n";
        $report .= "- Critical issues: {$this->stats['critical']}\n";
        $report .= "- Security events: {$this->stats['security_events']}\n";
        $report .= "- Database errors: {$this->stats['database_errors']}\n";
        $report .= "- Performance issues: {$this->stats['performance_issues']}\n\n";

        // Generate alerts based on thresholds
        $alerts = $this->generateAlerts();
        if (!empty($alerts)) {
            $report .= "🚨 ALERTS:\n";
            foreach ($alerts as $alert) {
                $report .= "- {$alert}\n";
            }
            $report .= "\n";
        }

        return $report;
    }

    private function generateAlerts(): array
    {
        $alerts = [];

        if ($this->stats['critical'] > 0) {
            $alerts[] = "CRITICAL: {$this->stats['critical']} critical issues detected!";
        }

        if ($this->stats['errors'] > 10) {
            $alerts[] = "HIGH: {$this->stats['errors']} errors found - investigate immediately";
        }

        if ($this->stats['database_errors'] > 5) {
            $alerts[] = "WARNING: {$this->stats['database_errors']} database errors detected";
        }

        if ($this->stats['security_events'] > 0) {
            $alerts[] = "SECURITY: {$this->stats['security_events']} security events logged";
        }

        if ($this->stats['performance_issues'] > 3) {
            $alerts[] = "PERFORMANCE: {$this->stats['performance_issues']} performance issues detected";
        }

        return $alerts;
    }
}

// Main execution
if ($argc > 1) {
    $logsDir = $argv[1];
} else {
    $logsDir = null;
}

$analyzer = new LogAnalyzer($logsDir);
$stats = $analyzer->analyze();
$report = $analyzer->generateReport();

echo $report;

// Save report to file
$reportFile = __DIR__ . '/log_analysis_report_' . date('Y-m-d_H-i-s') . '.txt';
file_put_contents($reportFile, $report);
echo "Report saved to: {$reportFile}\n";