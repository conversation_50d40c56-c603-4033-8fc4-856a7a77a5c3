#!/usr/bin/env php
<?php

/**
 * Log Monitoring Script for Symfony Application
 *
 * This script monitors log files in real-time and sends alerts
 * when certain thresholds are exceeded or specific patterns are detected.
 */

declare(strict_types=1);

class LogMonitor
{
    private string $logsDir;
    private array $alertThresholds;
    private array $alertHistory = [];

    public function __construct(string $logsDir = null)
    {
        $this->logsDir = $logsDir ?? __DIR__ . '/../../var/log';
        $this->alertThresholds = [
            'critical_per_minute' => 5,
            'errors_per_minute' => 20,
            'security_events_per_minute' => 10,
            'database_errors_per_minute' => 15,
        ];
    }

    public function monitor(int $durationMinutes = 5): void
    {
        echo "Starting log monitoring for {$durationMinutes} minutes...\n";
        echo "Monitoring directory: {$this->logsDir}\n";
        echo "Press Ctrl+C to stop\n\n";

        $startTime = time();
        $endTime = $startTime + ($durationMinutes * 60);

        while (time() < $endTime) {
            $this->checkLogs();
            sleep(30); // Check every 30 seconds
        }

        echo "\nMonitoring completed.\n";
        $this->generateSummaryReport();
    }

    private function checkLogs(): void
    {
        $logFiles = $this->findRecentLogFiles();

        foreach ($logFiles as $file) {
            $this->analyzeRecentEntries($file);
        }
    }

    private function findRecentLogFiles(): array
    {
        $files = [];

        if (!is_dir($this->logsDir)) {
            return $files;
        }

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($this->logsDir)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'log') {
                // Only check files modified in the last 5 minutes
                if ($file->getMTime() > (time() - 300)) {
                    $files[] = $file->getPathname();
                }
            }
        }

        return $files;
    }

    private function analyzeRecentEntries(string $filePath): void
    {
        $handle = fopen($filePath, 'r');
        if (!$handle) {
            return;
        }

        $recentLines = [];
        $fileSize = filesize($filePath);

        // Read last 1KB of the file to get recent entries
        if ($fileSize > 1024) {
            fseek($handle, -1024, SEEK_END);
            fgets($handle); // Skip partial line
        }

        while (($line = fgets($handle)) !== false) {
            $recentLines[] = $line;
        }

        fclose($handle);

        // Analyze only lines from the last minute
        $oneMinuteAgo = time() - 60;
        $recentEntries = array_filter($recentLines, function($line) use ($oneMinuteAgo) {
            // Try to extract timestamp from log line
            if (preg_match('/(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})/', $line, $matches)) {
                $timestamp = strtotime($matches[1]);
                return $timestamp > $oneMinuteAgo;
            }
            return true; // If no timestamp, include the line
        });

        $this->checkForAlerts($recentEntries, basename($filePath));
    }

    private function checkForAlerts(array $lines, string $filename): void
    {
        $stats = [
            'critical' => 0,
            'errors' => 0,
            'security' => 0,
            'database' => 0,
        ];

        foreach ($lines as $line) {
            $line = strtolower($line);

            if (preg_match('/\b(critical|emergency|alert)\b/i', $line)) {
                $stats['critical']++;
            }
            if (preg_match('/\b(error|exception|fatal)\b/i', $line)) {
                $stats['errors']++;
            }
            if (preg_match('/\b(security|auth|login|access|denied)\b/i', $line)) {
                $stats['security']++;
            }
            if (preg_match('/\b(database|pdo|sql|query)\b.*\b(error|failed|exception)\b/i', $line)) {
                $stats['database']++;
            }
        }

        // Check thresholds and send alerts
        $this->checkThreshold('critical', $stats['critical'], $filename);
        $this->checkThreshold('errors', $stats['errors'], $filename);
        $this->checkThreshold('security', $stats['security'], $filename);
        $this->checkThreshold('database', $stats['database'], $filename);
    }

    private function checkThreshold(string $type, int $count, string $filename): void
    {
        $threshold = $this->alertThresholds["{$type}_per_minute"] ?? 0;

        if ($count >= $threshold) {
            $alertKey = "{$type}_{$filename}";
            $now = time();

            // Prevent alert spam - only alert once per 5 minutes for the same issue
            if (!isset($this->alertHistory[$alertKey]) ||
                ($now - $this->alertHistory[$alertKey]) > 300) {

                $this->sendAlert($type, $count, $filename);
                $this->alertHistory[$alertKey] = $now;
            }
        }
    }

    private function sendAlert(string $type, int $count, string $filename): void
    {
        $timestamp = date('Y-m-d H:i:s');
        $message = "[{$timestamp}] 🚨 ALERT: {$count} {$type} events detected in {$filename} within the last minute!";

        // In a real implementation, you would:
        // - Send email notifications
        // - Send Slack/Discord messages
        // - Trigger PagerDuty alerts
        // - Write to external monitoring systems

        echo $message . "\n";

        // Log the alert
        $alertLog = __DIR__ . '/alerts.log';
        file_put_contents($alertLog, $message . "\n", FILE_APPEND);
    }

    private function generateSummaryReport(): void
    {
        $summary = "\n=== Monitoring Summary ===\n";
        $summary .= "Total alerts triggered: " . count($this->alertHistory) . "\n";

        if (!empty($this->alertHistory)) {
            $summary .= "Alert types:\n";
            foreach ($this->alertHistory as $alert => $timestamp) {
                $summary .= "- {$alert}: " . date('H:i:s', $timestamp) . "\n";
            }
        }

        echo $summary;

        // Save summary to file
        $summaryFile = __DIR__ . '/monitoring_summary_' . date('Y-m-d_H-i-s') . '.txt';
        file_put_contents($summaryFile, $summary);
        echo "Summary saved to: {$summaryFile}\n";
    }
}

// Main execution
$duration = $argc > 1 ? (int)$argv[1] : 5;
$logsDir = $argc > 2 ? $argv[2] : null;

$monitor = new LogMonitor($logsDir);
$monitor->monitor($duration);