#!/usr/bin/env php
<?php

/**
 * Error Tracking Integration Script
 *
 * This script provides integration with external error tracking services
 * and can send error notifications to various platforms.
 */

declare(strict_types=1);

class ErrorTracker
{
    private array $config;
    private string $logsDir;

    public function __construct(array $config = [], string $logsDir = null)
    {
        $this->config = array_merge([
            'sentry_dsn' => null,
            'slack_webhook' => null,
            'email_recipients' => [],
            'error_threshold' => 10,
        ], $config);

        $this->logsDir = $logsDir ?? __DIR__ . '/../../var/log';
    }

    public function trackErrors(): void
    {
        $errorFiles = $this->findErrorFiles();

        foreach ($errorFiles as $file) {
            $this->processErrorFile($file);
        }
    }

    private function findErrorFiles(): array
    {
        $files = [];

        if (!is_dir($this->logsDir)) {
            return $files;
        }

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($this->logsDir)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && strpos($file->getFilename(), 'error') !== false) {
                $files[] = $file->getPathname();
            }
        }

        return $files;
    }

    private function processErrorFile(string $filePath): void
    {
        $content = file_get_contents($filePath);
        if (!$content) {
            return;
        }

        $lines = explode("\n", $content);
        $errors = $this->extractErrors($lines);

        if (count($errors) >= $this->config['error_threshold']) {
            $this->sendErrorReport($errors, basename($filePath));
        }
    }

    private function extractErrors(array $lines): array
    {
        $errors = [];

        foreach ($lines as $line) {
            // Look for error patterns
            if (preg_match('/\b(ERROR|CRITICAL|FATAL|EMERGENCY|ALERT)\b/i', $line)) {
                $errors[] = [
                    'level' => $this->extractLogLevel($line),
                    'message' => trim($line),
                    'timestamp' => $this->extractTimestamp($line),
                ];
            }
        }

        return $errors;
    }

    private function extractLogLevel(string $line): string
    {
        if (preg_match('/\b(CRITICAL|EMERGENCY|ALERT)\b/i', $line, $matches)) {
            return strtoupper($matches[1]);
        }
        if (preg_match('/\b(ERROR|FATAL)\b/i', $line, $matches)) {
            return strtoupper($matches[1]);
        }
        return 'ERROR';
    }

    private function extractTimestamp(string $line): ?string
    {
        if (preg_match('/(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})/', $line, $matches)) {
            return $matches[1];
        }
        return null;
    }

    private function sendErrorReport(array $errors, string $filename): void
    {
        $report = $this->generateErrorReport($errors, $filename);

        // Send to configured services
        if ($this->config['sentry_dsn']) {
            $this->sendToSentry($report);
        }

        if ($this->config['slack_webhook']) {
            $this->sendToSlack($report);
        }

        if (!empty($this->config['email_recipients'])) {
            $this->sendEmail($report);
        }

        // Log the report locally
        $this->logReport($report);
    }

    private function generateErrorReport(array $errors, string $filename): array
    {
        return [
            'title' => "Error Report: {$filename}",
            'timestamp' => date('Y-m-d H:i:s'),
            'total_errors' => count($errors),
            'filename' => $filename,
            'errors' => array_slice($errors, -10), // Last 10 errors
            'summary' => $this->generateErrorSummary($errors),
        ];
    }

    private function generateErrorSummary(array $errors): array
    {
        $summary = [
            'CRITICAL' => 0,
            'EMERGENCY' => 0,
            'ALERT' => 0,
            'ERROR' => 0,
            'FATAL' => 0,
        ];

        foreach ($errors as $error) {
            $level = $error['level'];
            if (isset($summary[$level])) {
                $summary[$level]++;
            }
        }

        return $summary;
    }

    private function sendToSentry(array $report): void
    {
        // In a real implementation, you would use the Sentry SDK
        // For now, just log that we would send to Sentry
        echo "📊 Would send error report to Sentry\n";
        error_log("Sentry Error Report: " . json_encode($report));
    }

    private function sendToSlack(array $report): void
    {
        $message = [
            'text' => "🚨 Error Alert: {$report['total_errors']} errors detected in {$report['filename']}",
            'attachments' => [
                [
                    'color' => 'danger',
                    'fields' => [
                        [
                            'title' => 'Total Errors',
                            'value' => $report['total_errors'],
                            'short' => true,
                        ],
                        [
                            'title' => 'File',
                            'value' => $report['filename'],
                            'short' => true,
                        ],
                    ],
                ],
            ],
        ];

        // In a real implementation, you would make an HTTP request to Slack
        echo "📢 Would send Slack notification: {$message['text']}\n";
    }

    private function sendEmail(array $report): void
    {
        $subject = "Error Alert: {$report['total_errors']} errors in {$report['filename']}";
        $body = "Error Report Generated: {$report['timestamp']}\n\n";
        $body .= "Total Errors: {$report['total_errors']}\n";
        $body .= "File: {$report['filename']}\n\n";

        $body .= "Error Summary:\n";
        foreach ($report['summary'] as $level => $count) {
            if ($count > 0) {
                $body .= "- {$level}: {$count}\n";
            }
        }

        $body .= "\nRecent Errors:\n";
        foreach ($report['errors'] as $error) {
            $body .= "- [{$error['timestamp']}] {$error['level']}: {$error['message']}\n";
        }

        // In a real implementation, you would send the email
        echo "📧 Would send email notification to: " . implode(', ', $this->config['email_recipients']) . "\n";
        echo "Subject: {$subject}\n";
    }

    private function logReport(array $report): void
    {
        $logFile = __DIR__ . '/error_tracking.log';
        $logEntry = date('Y-m-d H:i:s') . " - Error report sent for {$report['filename']} ({$report['total_errors']} errors)\n";
        file_put_contents($logFile, $logEntry, FILE_APPEND);
    }
}

// Main execution
$config = [];

// Check for command line arguments
if ($argc > 1) {
    // Simple config parsing - in real app, use proper config files
    for ($i = 1; $i < $argc; $i++) {
        if (strpos($argv[$i], '--sentry=') === 0) {
            $config['sentry_dsn'] = substr($argv[$i], 9);
        } elseif (strpos($argv[$i], '--slack=') === 0) {
            $config['slack_webhook'] = substr($argv[$i], 8);
        } elseif (strpos($argv[$i], '--email=') === 0) {
            $config['email_recipients'] = explode(',', substr($argv[$i], 8));
        }
    }
}

$tracker = new ErrorTracker($config);
$tracker->trackErrors();

echo "Error tracking completed.\n";