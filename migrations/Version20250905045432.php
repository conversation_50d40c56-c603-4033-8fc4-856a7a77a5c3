<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250905045432 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SEQUENCE audit_log_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE configuration_history_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE doctors_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE patients_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE permissions_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE role_permissions_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE roles_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE security_events_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE system_configuration_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE user_2fa_settings_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE user_notifications_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE user_profiles_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE user_roles_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE user_verifications_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE users_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE audit_log (id BIGINT NOT NULL, user_id BIGINT DEFAULT NULL, action VARCHAR(50) NOT NULL, category VARCHAR(50) NOT NULL, resource_type VARCHAR(100) NOT NULL, resource_id INT DEFAULT NULL, old_values JSON DEFAULT NULL, new_values JSON DEFAULT NULL, ip_address VARCHAR(45) DEFAULT NULL, user_agent TEXT DEFAULT NULL, session_id VARCHAR(255) DEFAULT NULL, source VARCHAR(20) DEFAULT NULL, success BOOLEAN NOT NULL, timestamp TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_audit_log_user_id ON audit_log (user_id)');
        $this->addSql('CREATE INDEX idx_audit_log_action ON audit_log (action)');
        $this->addSql('CREATE INDEX idx_audit_log_category ON audit_log (category)');
        $this->addSql('CREATE INDEX idx_audit_log_resource_type ON audit_log (resource_type)');
        $this->addSql('CREATE INDEX idx_audit_log_resource_id ON audit_log (resource_id)');
        $this->addSql('CREATE INDEX idx_audit_log_timestamp ON audit_log (timestamp)');
        $this->addSql('CREATE INDEX idx_audit_log_success ON audit_log (success)');
        $this->addSql('CREATE TABLE configuration_history (id BIGINT NOT NULL, changed_by BIGINT NOT NULL, config_key VARCHAR(255) NOT NULL, old_value TEXT DEFAULT NULL, new_value TEXT DEFAULT NULL, change_reason TEXT DEFAULT NULL, ip_address VARCHAR(45) DEFAULT NULL, user_agent TEXT DEFAULT NULL, timestamp TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_configuration_history_config_key ON configuration_history (config_key)');
        $this->addSql('CREATE INDEX idx_configuration_history_changed_by ON configuration_history (changed_by)');
        $this->addSql('CREATE INDEX idx_configuration_history_timestamp ON configuration_history (timestamp)');
        $this->addSql('CREATE TABLE doctors (id BIGINT NOT NULL, prefix VARCHAR(20) DEFAULT NULL, first_name VARCHAR(255) NOT NULL, last_name VARCHAR(255) NOT NULL, suffix VARCHAR(20) DEFAULT NULL, registration_number VARCHAR(255) NOT NULL, registration_expiry_date DATE DEFAULT NULL, education JSON DEFAULT NULL, degree VARCHAR(100) NOT NULL, specialization VARCHAR(255) NOT NULL, years_of_experience INT DEFAULT NULL, bio TEXT DEFAULT NULL, picture VARCHAR(255) DEFAULT NULL, consultation_fee NUMERIC(10, 2) DEFAULT NULL, availability_schedule JSON DEFAULT NULL, status VARCHAR(20) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_B67687BE38CEDFBE ON doctors (registration_number)');
        $this->addSql('CREATE INDEX idx_doctors_name ON doctors (last_name, first_name)');
        $this->addSql('CREATE INDEX idx_doctors_registration_number ON doctors (registration_number)');
        $this->addSql('CREATE INDEX idx_doctors_specialization ON doctors (specialization)');
        $this->addSql('CREATE INDEX idx_doctors_degree ON doctors (degree)');
        $this->addSql('CREATE INDEX idx_doctors_consultation_fee ON doctors (consultation_fee)');
        $this->addSql('CREATE INDEX idx_doctors_status ON doctors (status)');
        $this->addSql('CREATE TABLE patients (id BIGINT NOT NULL, patient_id VARCHAR(50) NOT NULL, first_name VARCHAR(255) NOT NULL, last_name VARCHAR(255) NOT NULL, date_of_birth DATE NOT NULL, gender VARCHAR(50) NOT NULL, phone VARCHAR(20) NOT NULL, email VARCHAR(255) DEFAULT NULL, address JSON DEFAULT NULL, district VARCHAR(255) DEFAULT NULL, village VARCHAR(255) DEFAULT NULL, postal_code VARCHAR(20) DEFAULT NULL, emergency_contact_name VARCHAR(255) NOT NULL, emergency_contact_phone VARCHAR(20) NOT NULL, blood_type VARCHAR(10) DEFAULT NULL, height NUMERIC(5, 2) DEFAULT NULL, weight NUMERIC(5, 2) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_2CCC2E2C6B899279 ON patients (patient_id)');
        $this->addSql('CREATE INDEX idx_patients_name ON patients (last_name, first_name)');
        $this->addSql('CREATE INDEX idx_patients_patient_id ON patients (patient_id)');
        $this->addSql('CREATE INDEX idx_patients_district ON patients (district)');
        $this->addSql('CREATE INDEX idx_patients_village ON patients (village)');
        $this->addSql('CREATE TABLE permissions (id BIGINT NOT NULL, name VARCHAR(255) NOT NULL, display_name VARCHAR(255) NOT NULL, description TEXT DEFAULT NULL, resource VARCHAR(255) NOT NULL, action VARCHAR(50) NOT NULL, is_system_permission BOOLEAN NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_2DEDCC6F5E237E06 ON permissions (name)');
        $this->addSql('CREATE INDEX idx_permissions_name ON permissions (name)');
        $this->addSql('CREATE INDEX idx_permissions_resource ON permissions (resource)');
        $this->addSql('CREATE INDEX idx_permissions_action ON permissions (action)');
        $this->addSql('CREATE TABLE role_permissions (id BIGINT NOT NULL, role_id BIGINT DEFAULT NULL, permission_id BIGINT DEFAULT NULL, granted_by BIGINT DEFAULT NULL, granted_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_role_permissions_role_id ON role_permissions (role_id)');
        $this->addSql('CREATE INDEX idx_role_permissions_permission_id ON role_permissions (permission_id)');
        $this->addSql('CREATE INDEX idx_role_permissions_granted_by ON role_permissions (granted_by)');
        $this->addSql('CREATE TABLE roles (id BIGINT NOT NULL, parent_id BIGINT DEFAULT NULL, name VARCHAR(255) NOT NULL, display_name VARCHAR(255) NOT NULL, description TEXT DEFAULT NULL, is_system_role BOOLEAN NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_B63E2EC75E237E06 ON roles (name)');
        $this->addSql('CREATE INDEX idx_roles_name ON roles (name)');
        $this->addSql('CREATE INDEX idx_roles_parent_id ON roles (parent_id)');
        $this->addSql('CREATE TABLE security_events (id BIGINT NOT NULL, user_id BIGINT DEFAULT NULL, resolved_by BIGINT DEFAULT NULL, event_type VARCHAR(50) NOT NULL, category VARCHAR(50) NOT NULL, severity VARCHAR(20) NOT NULL, ip_address VARCHAR(45) DEFAULT NULL, user_agent TEXT DEFAULT NULL, location JSON DEFAULT NULL, details JSON DEFAULT NULL, correlation_id VARCHAR(255) DEFAULT NULL, source VARCHAR(20) DEFAULT NULL, is_resolved BOOLEAN NOT NULL, resolved_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, timestamp TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_6568A15F57EB21F9 ON security_events (resolved_by)');
        $this->addSql('CREATE INDEX idx_security_events_event_type ON security_events (event_type)');
        $this->addSql('CREATE INDEX idx_security_events_category ON security_events (category)');
        $this->addSql('CREATE INDEX idx_security_events_severity ON security_events (severity)');
        $this->addSql('CREATE INDEX idx_security_events_user_id ON security_events (user_id)');
        $this->addSql('CREATE INDEX idx_security_events_ip_address ON security_events (ip_address)');
        $this->addSql('CREATE INDEX idx_security_events_timestamp ON security_events (timestamp)');
        $this->addSql('CREATE INDEX idx_security_events_is_resolved ON security_events (is_resolved)');
        $this->addSql('CREATE INDEX idx_security_events_correlation_id ON security_events (correlation_id)');
        $this->addSql('CREATE TABLE system_configuration (id BIGINT NOT NULL, config_key VARCHAR(255) NOT NULL, config_value TEXT DEFAULT NULL, config_type VARCHAR(50) NOT NULL, category VARCHAR(50) NOT NULL, description TEXT DEFAULT NULL, is_system_setting BOOLEAN NOT NULL, is_editable BOOLEAN NOT NULL, validation_rules JSON DEFAULT NULL, default_value TEXT DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_DB40EA4295D1CAA6 ON system_configuration (config_key)');
        $this->addSql('CREATE INDEX idx_system_configuration_config_key ON system_configuration (config_key)');
        $this->addSql('CREATE INDEX idx_system_configuration_category ON system_configuration (category)');
        $this->addSql('CREATE INDEX idx_system_configuration_is_system_setting ON system_configuration (is_system_setting)');
        $this->addSql('CREATE TABLE user_2fa_settings (id BIGINT NOT NULL, user_id BIGINT DEFAULT NULL, secret_key VARCHAR(255) NOT NULL, backup_codes JSON DEFAULT NULL, enabled_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, last_used_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, method VARCHAR(20) NOT NULL, phone_number VARCHAR(20) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_A8C3D0C8A76ED395 ON user_2fa_settings (user_id)');
        $this->addSql('CREATE INDEX idx_user_2fa_settings_user_id ON user_2fa_settings (user_id)');
        $this->addSql('CREATE INDEX idx_user_2fa_settings_enabled_at ON user_2fa_settings (enabled_at)');
        $this->addSql('CREATE TABLE user_notifications (id BIGINT NOT NULL, user_id BIGINT DEFAULT NULL, created_by BIGINT DEFAULT NULL, title VARCHAR(255) NOT NULL, message TEXT NOT NULL, type VARCHAR(20) NOT NULL, priority VARCHAR(20) NOT NULL, channel VARCHAR(20) NOT NULL, is_read BOOLEAN NOT NULL, read_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, sent_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, expires_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, action_url VARCHAR(255) DEFAULT NULL, action_text VARCHAR(255) DEFAULT NULL, metadata JSON DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_8E8E1D83DE12AB56 ON user_notifications (created_by)');
        $this->addSql('CREATE INDEX idx_user_notifications_user_id ON user_notifications (user_id)');
        $this->addSql('CREATE INDEX idx_user_notifications_type ON user_notifications (type)');
        $this->addSql('CREATE INDEX idx_user_notifications_priority ON user_notifications (priority)');
        $this->addSql('CREATE INDEX idx_user_notifications_channel ON user_notifications (channel)');
        $this->addSql('CREATE INDEX idx_user_notifications_is_read ON user_notifications (is_read)');
        $this->addSql('CREATE INDEX idx_user_notifications_sent_at ON user_notifications (sent_at)');
        $this->addSql('CREATE INDEX idx_user_notifications_expires_at ON user_notifications (expires_at)');
        $this->addSql('CREATE INDEX idx_user_notifications_created_at ON user_notifications (created_at)');
        $this->addSql('CREATE TABLE user_profiles (id BIGINT NOT NULL, first_name VARCHAR(255) NOT NULL, last_name VARCHAR(255) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_user_profiles_name ON user_profiles (last_name, first_name)');
        $this->addSql('CREATE TABLE user_roles (id BIGINT NOT NULL, user_id BIGINT DEFAULT NULL, role_id BIGINT DEFAULT NULL, assigned_by BIGINT DEFAULT NULL, assigned_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, notes TEXT DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_user_roles_user_id ON user_roles (user_id)');
        $this->addSql('CREATE INDEX idx_user_roles_role_id ON user_roles (role_id)');
        $this->addSql('CREATE INDEX idx_user_roles_assigned_by ON user_roles (assigned_by)');
        $this->addSql('CREATE UNIQUE INDEX uniq_user_roles_user_role ON user_roles (user_id, role_id)');
        $this->addSql('CREATE TABLE user_verifications (id BIGINT NOT NULL, user_id BIGINT DEFAULT NULL, verification_type VARCHAR(50) NOT NULL, verification_method VARCHAR(50) DEFAULT NULL, token VARCHAR(255) NOT NULL, expires_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, verified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, ip_address VARCHAR(45) DEFAULT NULL, user_agent TEXT DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_62084A165F37A13B ON user_verifications (token)');
        $this->addSql('CREATE INDEX idx_user_verifications_user_id ON user_verifications (user_id)');
        $this->addSql('CREATE INDEX idx_user_verifications_verification_type ON user_verifications (verification_type)');
        $this->addSql('CREATE INDEX idx_user_verifications_verification_method ON user_verifications (verification_method)');
        $this->addSql('CREATE INDEX idx_user_verifications_token ON user_verifications (token)');
        $this->addSql('CREATE INDEX idx_user_verifications_expires_at ON user_verifications (expires_at)');
        $this->addSql('CREATE INDEX idx_user_verifications_verified_at ON user_verifications (verified_at)');
        $this->addSql('CREATE INDEX idx_user_verifications_ip_address ON user_verifications (ip_address)');
        $this->addSql('CREATE TABLE users (id BIGINT NOT NULL, profile_id BIGINT DEFAULT NULL, doctor_id BIGINT DEFAULT NULL, patient_id BIGINT DEFAULT NULL, email VARCHAR(255) NOT NULL, phone VARCHAR(255) DEFAULT NULL, password VARCHAR(255) NOT NULL, roles JSON NOT NULL, status VARCHAR(50) DEFAULT \'pending\' NOT NULL, last_login_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_1483A5E9E7927C74 ON users (email)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_1483A5E9444F97DD ON users (phone)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_1483A5E9CCFA12B8 ON users (profile_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_1483A5E987F4FB17 ON users (doctor_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_1483A5E96B899279 ON users (patient_id)');
        $this->addSql('CREATE INDEX idx_users_email ON users (email)');
        $this->addSql('CREATE INDEX idx_users_phone ON users (phone)');
        $this->addSql('CREATE INDEX idx_users_status ON users (status)');
        $this->addSql('CREATE INDEX idx_users_profile_id ON users (profile_id)');
        $this->addSql('CREATE INDEX idx_users_doctor_id ON users (doctor_id)');
        $this->addSql('CREATE INDEX idx_users_patient_id ON users (patient_id)');
        $this->addSql('CREATE TABLE messenger_messages (id BIGSERIAL NOT NULL, body TEXT NOT NULL, headers TEXT NOT NULL, queue_name VARCHAR(190) NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, available_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, delivered_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_75EA56E0FB7336F0 ON messenger_messages (queue_name)');
        $this->addSql('CREATE INDEX IDX_75EA56E0E3BD61CE ON messenger_messages (available_at)');
        $this->addSql('CREATE INDEX IDX_75EA56E016BA31DB ON messenger_messages (delivered_at)');
        $this->addSql('COMMENT ON COLUMN messenger_messages.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN messenger_messages.available_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN messenger_messages.delivered_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE OR REPLACE FUNCTION notify_messenger_messages() RETURNS TRIGGER AS $$
            BEGIN
                PERFORM pg_notify(\'messenger_messages\', NEW.queue_name::text);
                RETURN NEW;
            END;
        $$ LANGUAGE plpgsql;');
        $this->addSql('DROP TRIGGER IF EXISTS notify_trigger ON messenger_messages;');
        $this->addSql('CREATE TRIGGER notify_trigger AFTER INSERT OR UPDATE ON messenger_messages FOR EACH ROW EXECUTE PROCEDURE notify_messenger_messages();');
        $this->addSql('ALTER TABLE audit_log ADD CONSTRAINT FK_F6E1C0F5A76ED395 FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE configuration_history ADD CONSTRAINT FK_A9F2B89510BC6D9F FOREIGN KEY (changed_by) REFERENCES users (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE role_permissions ADD CONSTRAINT FK_1FBA94E6D60322AC FOREIGN KEY (role_id) REFERENCES roles (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE role_permissions ADD CONSTRAINT FK_1FBA94E6FED90CCA FOREIGN KEY (permission_id) REFERENCES permissions (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE role_permissions ADD CONSTRAINT FK_1FBA94E6A5FB753F FOREIGN KEY (granted_by) REFERENCES users (id) ON DELETE RESTRICT NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE roles ADD CONSTRAINT FK_B63E2EC7727ACA70 FOREIGN KEY (parent_id) REFERENCES roles (id) ON DELETE SET NULL NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE security_events ADD CONSTRAINT FK_6568A15FA76ED395 FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE security_events ADD CONSTRAINT FK_6568A15F57EB21F9 FOREIGN KEY (resolved_by) REFERENCES users (id) ON DELETE SET NULL NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE user_2fa_settings ADD CONSTRAINT FK_A8C3D0C8A76ED395 FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE user_notifications ADD CONSTRAINT FK_8E8E1D83A76ED395 FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE user_notifications ADD CONSTRAINT FK_8E8E1D83DE12AB56 FOREIGN KEY (created_by) REFERENCES users (id) ON DELETE SET NULL NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE user_roles ADD CONSTRAINT FK_54FCD59FA76ED395 FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE user_roles ADD CONSTRAINT FK_54FCD59FD60322AC FOREIGN KEY (role_id) REFERENCES roles (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE user_roles ADD CONSTRAINT FK_54FCD59F61A2AF17 FOREIGN KEY (assigned_by) REFERENCES users (id) ON DELETE RESTRICT NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE user_verifications ADD CONSTRAINT FK_62084A16A76ED395 FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE users ADD CONSTRAINT FK_1483A5E9CCFA12B8 FOREIGN KEY (profile_id) REFERENCES user_profiles (id) ON DELETE SET NULL NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE users ADD CONSTRAINT FK_1483A5E987F4FB17 FOREIGN KEY (doctor_id) REFERENCES doctors (id) ON DELETE SET NULL NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE users ADD CONSTRAINT FK_1483A5E96B899279 FOREIGN KEY (patient_id) REFERENCES patients (id) ON DELETE SET NULL NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SCHEMA public');
        $this->addSql('DROP SEQUENCE audit_log_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE configuration_history_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE doctors_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE patients_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE permissions_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE role_permissions_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE roles_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE security_events_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE system_configuration_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE user_2fa_settings_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE user_notifications_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE user_profiles_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE user_roles_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE user_verifications_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE users_id_seq CASCADE');
        $this->addSql('ALTER TABLE audit_log DROP CONSTRAINT FK_F6E1C0F5A76ED395');
        $this->addSql('ALTER TABLE configuration_history DROP CONSTRAINT FK_A9F2B89510BC6D9F');
        $this->addSql('ALTER TABLE role_permissions DROP CONSTRAINT FK_1FBA94E6D60322AC');
        $this->addSql('ALTER TABLE role_permissions DROP CONSTRAINT FK_1FBA94E6FED90CCA');
        $this->addSql('ALTER TABLE role_permissions DROP CONSTRAINT FK_1FBA94E6A5FB753F');
        $this->addSql('ALTER TABLE roles DROP CONSTRAINT FK_B63E2EC7727ACA70');
        $this->addSql('ALTER TABLE security_events DROP CONSTRAINT FK_6568A15FA76ED395');
        $this->addSql('ALTER TABLE security_events DROP CONSTRAINT FK_6568A15F57EB21F9');
        $this->addSql('ALTER TABLE user_2fa_settings DROP CONSTRAINT FK_A8C3D0C8A76ED395');
        $this->addSql('ALTER TABLE user_notifications DROP CONSTRAINT FK_8E8E1D83A76ED395');
        $this->addSql('ALTER TABLE user_notifications DROP CONSTRAINT FK_8E8E1D83DE12AB56');
        $this->addSql('ALTER TABLE user_roles DROP CONSTRAINT FK_54FCD59FA76ED395');
        $this->addSql('ALTER TABLE user_roles DROP CONSTRAINT FK_54FCD59FD60322AC');
        $this->addSql('ALTER TABLE user_roles DROP CONSTRAINT FK_54FCD59F61A2AF17');
        $this->addSql('ALTER TABLE user_verifications DROP CONSTRAINT FK_62084A16A76ED395');
        $this->addSql('ALTER TABLE users DROP CONSTRAINT FK_1483A5E9CCFA12B8');
        $this->addSql('ALTER TABLE users DROP CONSTRAINT FK_1483A5E987F4FB17');
        $this->addSql('ALTER TABLE users DROP CONSTRAINT FK_1483A5E96B899279');
        $this->addSql('DROP TABLE audit_log');
        $this->addSql('DROP TABLE configuration_history');
        $this->addSql('DROP TABLE doctors');
        $this->addSql('DROP TABLE patients');
        $this->addSql('DROP TABLE permissions');
        $this->addSql('DROP TABLE role_permissions');
        $this->addSql('DROP TABLE roles');
        $this->addSql('DROP TABLE security_events');
        $this->addSql('DROP TABLE system_configuration');
        $this->addSql('DROP TABLE user_2fa_settings');
        $this->addSql('DROP TABLE user_notifications');
        $this->addSql('DROP TABLE user_profiles');
        $this->addSql('DROP TABLE user_roles');
        $this->addSql('DROP TABLE user_verifications');
        $this->addSql('DROP TABLE users');
        $this->addSql('DROP TABLE messenger_messages');
    }
}
